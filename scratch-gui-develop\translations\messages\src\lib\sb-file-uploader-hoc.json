[{"id": "gui.projectLoader.loadError", "description": "An error that displays when a local project file fails to load.", "defaultMessage": "The project file that was selected failed to load."}, {"id": "gui.projectLoader.corruptedError", "description": "An error that displays when a project file is corrupted.", "defaultMessage": "The project file is corrupted."}, {"id": "gui.projectLoader.invalidFileFormat", "description": "An error that displays when a project file format is invalid.", "defaultMessage": "The project file format is invalid."}]