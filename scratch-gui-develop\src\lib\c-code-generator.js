/**
 * 将Scratch块转换为C代码的工具函数库
 */

/**
 * 将Scratch积木转换为对应的C代码
 * @param {Object} target - 编辑目标对象
 * @param {Object} vm - Scratch虚拟机实例
 * @returns {string} 生成的C代码
 */
const generateCCodeFromBlocks = (target, vm) => {
    if (!target || !vm) {
        return '// 没有积木块可以转换';
    }

    let cCode = '#include <stdio.h>\n#include <stdlib.h>\n#include <unistd.h> // sleep函数\n\n';

    // 收集所有声明的变量
    const variables = [];
    if (target.variables) {
        Object.keys(target.variables).forEach(id => {
            const variable = target.variables[id];
            if (variable) {
                variables.push({
                    id: id,
                    name: variable.name,
                    value: variable.value
                });
            }
        });
    }

    // 添加变量声明
    if (variables.length > 0) {
        cCode += '// 变量声明\n';
        variables.forEach(variable => {
            // 根据变量值类型选择适当的C类型
            let type = 'int';
            let value = variable.value;
            if (typeof value === 'string') {
                type = 'char*';
                value = `"${value}"`;
            } else if (typeof value === 'boolean') {
                type = 'int'; // C中没有布尔类型，用int
                value = value ? '1' : '0';
            }
            cCode += `${type} ${variable.name} = ${value};\n`;
        });
        cCode += '\n';
    }

    // 声明需要使用的函数
    cCode += '// 为使用K1机器人函数添加的函数声明\n';
    cCode += 'void forward(int speed, int destance);\n';
    cCode += 'void back(int speed, int destance);\n';
    cCode += 'void turn_left(int degree);\n';
    cCode += 'void turn_right(int degree);\n';
    cCode += 'void gpp_say(int mode, void *str);\n';
    cCode += 'int servo_open(void);\n';
    cCode += 'int servo_close(void);\n';
    cCode += 'int tracker_start(void);\n';
    cCode += 'int tracker_close(void);\n';
    cCode += 'void beep(int bound, int time);\n';
    cCode += 'void colorful_led(int mode, int rgb);\n';
    cCode += 'void Set_CScript_Mode(int mode);\n';
    cCode += 'void cexit(void);\n';
    cCode += 'int lightsensor(void);\n';
    cCode += 'int distsensor(void);\n';
    cCode += 'int mic1sensor(void);\n';
    cCode += 'int tracker(int id);\n';
    cCode += 'int Get_Ps2Value(void);\n\n';

    try {
        // 获取所有脚本（即顶层块）
        const blocks = target.blocks;
        if (!blocks) {
            throw new Error('无法访问积木块');
        }

        // 不同方式尝试获取脚本列表
        let scripts = [];
        if (blocks._scripts && blocks._scripts.length > 0) {
            scripts = blocks._scripts;
        } else if (blocks.getScripts) {
            scripts = blocks.getScripts();
        } else {
            // 尝试找到顶层块（没有父块的块）
            const topBlocks = [];
            Object.keys(blocks._blocks || {}).forEach(id => {
                const block = blocks._blocks[id];
                if (block && !block.parent) {
                    topBlocks.push(id);
                }
            });
            scripts = topBlocks;
        }

        if (!scripts || scripts.length === 0) {
            cCode += 'void main() {\n';
            cCode += '    // 没有脚本块\n';
            cCode += '}\n';
            return cCode;
        }

        // 遍历每个脚本并生成代码
        let userFunctions = '';
        let mainCode = '';
        for (let i = 0; i < scripts.length; i++) {
            const topBlockId = scripts[i];
            const block = blocks._blocks[topBlockId];

            // 如果是函数定义块，则加入用户自定义函数
            if (block && block.opcode === 'procedures_definition') {
                userFunctions += processBlock(topBlockId, blocks, 0) + '\n';
            } else {
                // 否则是主程序代码
                mainCode += processBlock(topBlockId, blocks, 1); // 1表示缩进级别
            }
        }

        // 先添加用户函数，再添加主程序
        cCode += userFunctions;

        // 根据K1机器人特性，函数调用直接在程序体外调用
        cCode += mainCode;

        // 添加必要的main函数（根据K1机器人要求）
        cCode += 'void main() {}\n';
    } catch (e) {
        console.error('C代码生成错误:', e);
        cCode += `// 代码生成出错: ${e.message}\n`;
        cCode += 'void main() {}\n';
    }

    return cCode;
};

/**
 * 处理单个块并生成相应的C代码
 * @param {string} blockId - 块ID
 * @param {Object} blocks - 块对象
 * @param {number} indentLevel - 缩进级别
 * @returns {string} 生成的代码
 */
const processBlock = (blockId, blocks, indentLevel) => {
    if (!blockId || !blocks) return '';

    // 尝试多种方式获取块
    let block = null;
    if (blocks.getBlock) {
        block = blocks.getBlock(blockId);
    } else if (blocks._blocks && blocks._blocks[blockId]) {
        block = blocks._blocks[blockId];
    }

    if (!block) {
        console.warn('找不到积木块:', blockId);
        return '';
    }

    const indent = '    '.repeat(indentLevel);
    let code = '';

    try {
        // 根据操作码类型生成不同的C代码
        switch (block.opcode) {
            // 事件类
            case 'event_whenflagclicked':
                code += `${indent}// 当绿旗被点击时\n`;
                code += `${indent}gpp_say(1, "程序开始运行");\n`;
                break;
            case 'event_whenkeypressed':
                const keyOption = block.fields && block.fields.KEY_OPTION ? block.fields.KEY_OPTION.value : 'space';
                code += `${indent}// 当按下按键 ${keyOption}\n`;
                code += `${indent}gpp_say(1, "按下了 ${keyOption} 键");\n`;
                break;

            // 控制类
            case 'control_repeat':
                const times = getInputBlock(block, blocks, 'TIMES');
                const timesValue = times ? getInputValue(times, blocks) : '10';
                code += `${indent}// 重复执行\n`;
                code += `${indent}for (int i = 0; i < ${timesValue}; i++) {\n`;
                const repeatBlockId = getInputBlock(block, blocks, 'SUBSTACK');
                if (repeatBlockId) {
                    code += processBlock(repeatBlockId, blocks, indentLevel + 1);
                }
                code += `${indent}}\n`;
                break;
            case 'control_forever':
                code += `${indent}// 一直重复执行\n`;
                code += `${indent}while (1) {\n`;
                const foreverBlockId = getInputBlock(block, blocks, 'SUBSTACK');
                if (foreverBlockId) {
                    code += processBlock(foreverBlockId, blocks, indentLevel + 1);
                }
                code += `${indent}}\n`;
                break;
            case 'control_if':
                const condition = getInputBlock(block, blocks, 'CONDITION');
                const conditionCode = condition ? getExpressionCode(condition, blocks) : 'true';
                code += `${indent}if (${conditionCode}) {\n`;
                const ifBlockId = getInputBlock(block, blocks, 'SUBSTACK');
                if (ifBlockId) {
                    code += processBlock(ifBlockId, blocks, indentLevel + 1);
                }
                code += `${indent}}\n`;
                break;
            case 'control_if_else':
                const ifElseCondition = getInputBlock(block, blocks, 'CONDITION');
                const ifElseConditionCode = ifElseCondition ? getExpressionCode(ifElseCondition, blocks) : 'true';
                code += `${indent}if (${ifElseConditionCode}) {\n`;
                const ifElseBlockId = getInputBlock(block, blocks, 'SUBSTACK');
                if (ifElseBlockId) {
                    code += processBlock(ifElseBlockId, blocks, indentLevel + 1);
                }
                code += `${indent}} else {\n`;
                const elseBlockId = getInputBlock(block, blocks, 'SUBSTACK2');
                if (elseBlockId) {
                    code += processBlock(elseBlockId, blocks, indentLevel + 1);
                }
                code += `${indent}}\n`;
                break;
            case 'control_wait':
                const waitDuration = getInputBlock(block, blocks, 'DURATION');
                const waitValue = waitDuration ? getInputValue(waitDuration, blocks) : '1';
                code += `${indent}// 等待 ${waitValue} 秒\n`;
                code += `${indent}sleep(${waitValue}); \n`;
                break;

            // 运动类 - 使用K1机器人对应函数
            case 'motion_movesteps':
                const steps = getInputBlock(block, blocks, 'STEPS');
                const stepsValue = steps ? getInputValue(steps, blocks) : '10';
                code += `${indent}// 移动 ${stepsValue} 步\n`;
                // 使用forward函数，速度设为4（中速），距离根据步数
                code += `${indent}forward(4, ${stepsValue});\n`;
                break;
            case 'motion_turnright':
                const angleRight = getInputBlock(block, blocks, 'DEGREES');
                const angleRightValue = angleRight ? getInputValue(angleRight, blocks) : '15';
                code += `${indent}// 向右旋转 ${angleRightValue} 度\n`;
                code += `${indent}turn_right(${angleRightValue});\n`;
                break;
            case 'motion_turnleft':
                const angleLeft = getInputBlock(block, blocks, 'DEGREES');
                const angleLeftValue = angleLeft ? getInputValue(angleLeft, blocks) : '15';
                code += `${indent}// 向左旋转 ${angleLeftValue} 度\n`;
                code += `${indent}turn_left(${angleLeftValue});\n`;
                break;
            case 'motion_gotoxy':
                const x = getInputBlock(block, blocks, 'X');
                const y = getInputBlock(block, blocks, 'Y');
                const xValue = x ? getInputValue(x, blocks) : '0';
                const yValue = y ? getInputValue(y, blocks) : '0';
                code += `${indent}// 移动到坐标 (${xValue}, ${yValue})\n`;
                // 对于坐标移动，使用printf，因为K1机器人没有直接对应函数
                code += `${indent}printf("移动到坐标 (%s, %s)\\n", "${xValue}", "${yValue}");\n`;
                break;

            // 外观类 - 使用K1机器人对应函数
            case 'looks_say':
            case 'looks_sayforsecs':
                const message = getInputBlock(block, blocks, 'MESSAGE');
                const messageValue = message ? getInputValue(message, blocks) : '"Hello!"';
                code += `${indent}// 说话\n`;
                code += `${indent}gpp_say(1, ${messageValue});\n`;
                break;
            case 'looks_think':
            case 'looks_thinkforsecs':
                const thought = getInputBlock(block, blocks, 'MESSAGE');
                const thoughtValue = thought ? getInputValue(thought, blocks) : '"Hmm..."';
                code += `${indent}// 思考\n`;
                // 思考也使用gpp_say，但可以添加一个前缀表示是思考
                code += `${indent}gpp_say(1, "思考中: ");\n`;
                code += `${indent}gpp_say(1, ${thoughtValue});\n`;
                break;
            case 'looks_switchcostumeto':
                const costume = getInputBlock(block, blocks, 'COSTUME');
                const costumeValue = costume ? getInputValue(costume, blocks) : '"costume1"';
                code += `${indent}// 换成造型 ${costumeValue}\n`;
                // 使用colorful_led函数模拟造型切换
                code += `${indent}colorful_led(3, 1);\n`;
                code += `${indent}gpp_say(1, "切换造型");\n`;
                break;

            // 声音类 - 使用K1机器人对应函数
            case 'sound_play':
                const sound = getInputBlock(block, blocks, 'SOUND_MENU');
                const soundValue = sound ? getInputValue(sound, blocks) : '"meow"';
                code += `${indent}// 播放声音 ${soundValue}\n`;
                // 使用beep函数播放声音
                code += `${indent}beep(1000, 500); // 播放1000Hz的声音，持续500毫秒\n`;
                break;

            // 数据类
            case 'data_setvariableto':
                try {
                    const varName = block.fields && block.fields.VARIABLE ? block.fields.VARIABLE.value : 'var';
                    const varValue = getInputBlock(block, blocks, 'VALUE');
                    const value = varValue ? getInputValue(varValue, blocks) : '0';
                    code += `${indent}${varName} = ${value};\n`;
                    code += `${indent}gpp_say(1, "设置变量");\n`;
                } catch (e) {
                    code += `${indent}// 设置变量出错: ${e.message}\n`;
                }
                break;
            case 'data_changevariableby':
                try {
                    const changeVarName = block.fields && block.fields.VARIABLE ? block.fields.VARIABLE.value : 'var';
                    const changeVal = getInputBlock(block, blocks, 'VALUE');
                    const changeValue = changeVal ? getInputValue(changeVal, blocks) : '1';
                    code += `${indent}${changeVarName} += ${changeValue};\n`;
                    code += `${indent}gpp_say(1, "变量增加");\n`;
                } catch (e) {
                    code += `${indent}// 改变变量出错: ${e.message}\n`;
                }
                break;
            case 'data_showvariable':
            case 'data_hidevariable':
                try {
                    const varName = block.fields && block.fields.VARIABLE ? block.fields.VARIABLE.value : 'var';
                    const action = block.opcode === 'data_showvariable' ? '显示' : '隐藏';
                    code += `${indent}// ${action}变量 ${varName}\n`;
                    code += `${indent}gpp_say(1, "${action}变量: ${varName}");\n`;
                } catch (e) {
                    code += `${indent}// ${block.opcode}出错: ${e.message}\n`;
                }
                break;

            // 运算类
            case 'operator_add':
            case 'operator_subtract':
            case 'operator_multiply':
            case 'operator_divide':
                const operatorMap = {
                    'operator_add': '+',
                    'operator_subtract': '-',
                    'operator_multiply': '*',
                    'operator_divide': '/'
                };
                const num1 = getInputBlock(block, blocks, 'NUM1');
                const num2 = getInputBlock(block, blocks, 'NUM2');
                const num1Value = num1 ? getInputValue(num1, blocks) : '0';
                const num2Value = num2 ? getInputValue(num2, blocks) : '0';
                code += `${indent}(${num1Value} ${operatorMap[block.opcode]} ${num2Value})`;
                break;
            case 'operator_random':
                const from = getInputBlock(block, blocks, 'FROM');
                const to = getInputBlock(block, blocks, 'TO');
                const fromValue = from ? getInputValue(from, blocks) : '1';
                const toValue = to ? getInputValue(to, blocks) : '10';
                code += `${indent}// 生成从 ${fromValue} 到 ${toValue} 的随机数\n`;
                code += `${indent}(rand() % (${toValue} - ${fromValue} + 1) + ${fromValue})`;
                break;

            // 传感器类 - 使用K1机器人对应函数
            case 'sensing_timer':
                code += `${indent}// 获取计时器值\n`;
                code += `${indent}distsensor() // 使用距离传感器代替计时器\n`;
                break;
            case 'sensing_loudness':
                code += `${indent}// 获取声音响度\n`;
                code += `${indent}mic1sensor() // 使用噪声检测传感器\n`;
                break;
            case 'sensing_light':
                code += `${indent}// 获取光线强度\n`;
                code += `${indent}lightsensor() // 使用光线传感器\n`;
                break;

            // K1机器人自定义积木块
            case 'k1_forward':
                const forwardSpeed = getInputBlock(block, blocks, 'SPEED');
                const forwardDistance = getInputBlock(block, blocks, 'DISTANCE');
                const forwardSpeedValue = forwardSpeed ? getInputValue(forwardSpeed, blocks) : '4';
                const forwardDistanceValue = forwardDistance ? getInputValue(forwardDistance, blocks) : '10';
                code += `${indent}// 前进，速度: ${forwardSpeedValue}，距离: ${forwardDistanceValue}\n`;
                code += `${indent}forward(${forwardSpeedValue}, ${forwardDistanceValue});\n`;
                break;
            case 'k1_back':
                const backSpeed = getInputBlock(block, blocks, 'SPEED');
                const backDistance = getInputBlock(block, blocks, 'DISTANCE');
                const backSpeedValue = backSpeed ? getInputValue(backSpeed, blocks) : '4';
                const backDistanceValue = backDistance ? getInputValue(backDistance, blocks) : '10';
                code += `${indent}// 后退，速度: ${backSpeedValue}，距离: ${backDistanceValue}\n`;
                code += `${indent}back(${backSpeedValue}, ${backDistanceValue});\n`;
                break;
            case 'k1_turn_left':
                const leftDegree = getInputBlock(block, blocks, 'DEGREE');
                const leftDegreeValue = leftDegree ? getInputValue(leftDegree, blocks) : '90';
                code += `${indent}// 左转，角度: ${leftDegreeValue}\n`;
                code += `${indent}turn_left(${leftDegreeValue});\n`;
                break;
            case 'k1_turn_right':
                const rightDegree = getInputBlock(block, blocks, 'DEGREE');
                const rightDegreeValue = rightDegree ? getInputValue(rightDegree, blocks) : '90';
                code += `${indent}// 右转，角度: ${rightDegreeValue}\n`;
                code += `${indent}turn_right(${rightDegreeValue});\n`;
                break;
            case 'k1_gpp_say':
                const sayMode = getInputBlock(block, blocks, 'MODE');
                const sayText = getInputBlock(block, blocks, 'TEXT');
                const sayModeValue = sayMode ? getInputValue(sayMode, blocks) : '1';
                const sayTextValue = sayText ? getInputValue(sayText, blocks) : '"Hello!"';
                code += `${indent}// 说话，模式: ${sayModeValue}，内容: ${sayTextValue}\n`;
                code += `${indent}gpp_say(${sayModeValue}, ${sayTextValue});\n`;
                break;
            case 'k1_servo_open':
                code += `${indent}// 打开机械手\n`;
                code += `${indent}servo_open();\n`;
                break;
            case 'k1_servo_close':
                code += `${indent}// 关闭机械手\n`;
                code += `${indent}servo_close();\n`;
                break;
            case 'k1_tracker_start':
                code += `${indent}// 开启巡线模式\n`;
                code += `${indent}tracker_start();\n`;
                break;
            case 'k1_tracker_close':
                code += `${indent}// 关闭巡线模式\n`;
                code += `${indent}tracker_close();\n`;
                break;
            case 'k1_beep':
                const beepBound = getInputBlock(block, blocks, 'BOUND');
                const beepTime = getInputBlock(block, blocks, 'TIME');
                const beepBoundValue = beepBound ? getInputValue(beepBound, blocks) : '1000';
                const beepTimeValue = beepTime ? getInputValue(beepTime, blocks) : '500';
                code += `${indent}// 蜂鸣器，频率: ${beepBoundValue}，时间: ${beepTimeValue}\n`;
                code += `${indent}beep(${beepBoundValue}, ${beepTimeValue});\n`;
                break;
            case 'k1_colorful_led':
                const ledMode = getInputBlock(block, blocks, 'MODE');
                const ledRgb = getInputBlock(block, blocks, 'RGB');
                const ledModeValue = ledMode ? getInputValue(ledMode, blocks) : '3';
                const ledRgbValue = ledRgb ? getInputValue(ledRgb, blocks) : '1';
                code += `${indent}// LED灯，模式: ${ledModeValue}，颜色: ${ledRgbValue}\n`;
                code += `${indent}colorful_led(${ledModeValue}, ${ledRgbValue});\n`;
                break;
            case 'k1_set_cscript_mode':
                const scriptMode = getInputBlock(block, blocks, 'MODE');
                const scriptModeValue = scriptMode ? getInputValue(scriptMode, blocks) : '2';
                code += `${indent}// 设置C程序运行模式: ${scriptModeValue}\n`;
                code += `${indent}Set_CScript_Mode(${scriptModeValue});\n`;
                break;
            case 'k1_cexit':
                code += `${indent}// 停止当前C程序\n`;
                code += `${indent}cexit();\n`;
                break;
            // 光线传感器 - 命令块版本
            case 'k1_lightsensor':
                code += `${indent}// 读取光线传感器数值\n`;
                code += `${indent}int lightValue = lightsensor();\n`;
                code += `${indent}printf("光线传感器数值: %d\\n", lightValue);\n`;
                break;
            // 光线传感器 - 报告块版本
            case 'k1_lightsensor_reporter':
                code += `lightsensor()`;
                break;

            // 距离传感器 - 命令块版本
            case 'k1_distsensor':
                code += `${indent}// 读取距离传感器数值\n`;
                code += `${indent}int distValue = distsensor();\n`;
                code += `${indent}printf("距离传感器数值: %d 厘米\\n", distValue);\n`;
                break;
            // 距离传感器 - 报告块版本
            case 'k1_distsensor_reporter':
                code += `distsensor()`;
                break;

            // 噪声传感器 - 命令块版本
            case 'k1_mic1sensor':
                code += `${indent}// 读取噪声传感器数值\n`;
                code += `${indent}int micValue = mic1sensor();\n`;
                code += `${indent}printf("噪声传感器数值: %d\\n", micValue);\n`;
                break;
            // 噪声传感器 - 报告块版本
            case 'k1_mic1sensor_reporter':
                code += `mic1sensor()`;
                break;

            // 循迹传感器 - 命令块版本
            case 'k1_tracker':
                const trackerId = getInputBlock(block, blocks, 'ID');
                const trackerIdValue = trackerId ? getInputValue(trackerId, blocks) : '1';
                code += `${indent}// 读取循迹传感器数值\n`;
                code += `${indent}int trackerValue = tracker(${trackerIdValue});\n`;
                code += `${indent}printf("循迹传感器 %d 数值: %d\\n", ${trackerIdValue}, trackerValue);\n`;
                break;
            // 循迹传感器 - 报告块版本
            case 'k1_tracker_reporter':
                const trackerReporterId = getInputBlock(block, blocks, 'ID');
                const trackerReporterIdValue = trackerReporterId ? getInputValue(trackerReporterId, blocks) : '1';
                code += `tracker(${trackerReporterIdValue})`;
                break;

            // 手柄按键值 - 命令块版本
            case 'k1_get_ps2value':
                code += `${indent}// 读取手柄按键值\n`;
                code += `${indent}int ps2Value = Get_Ps2Value();\n`;
                code += `${indent}printf("手柄按键值: %d\\n", ps2Value);\n`;
                break;
            // 手柄按键值 - 报告块版本
            case 'k1_get_ps2value_reporter':
                code += `Get_Ps2Value()`;
                break;

            // 默认情况
            default:
                code += `${indent}printf("执行 %s 操作\\n", "${block.opcode}");\n`;
        }
    } catch (e) {
        console.error('处理积木块出错:', e, block);
        code += `${indent}// 处理积木块出错: ${e.message}\n`;
    }

    // 处理下一个块
    const nextBlockId = block.next;
    if (nextBlockId) {
        code += processBlock(nextBlockId, blocks, indentLevel);
    }

    return code;
};

/**
 * 获取输入块ID
 * @param {Object} block - 块对象
 * @param {Object} blocks - 所有块
 * @param {string} inputName - 输入名称
 * @returns {string|null} 输入块ID
 */
const getInputBlock = (block, blocks, inputName) => {
    try {
        if (!block || !block.inputs) return null;

        const input = block.inputs[inputName];
        if (!input) return null;

        // 尝试多种可能的结构
        if (input.hasOwnProperty('block')) {
            return input.block;
        } else if (Array.isArray(input) && input.length > 1) {
            return input[1];
        } else if (typeof input === 'object') {
            // 检查是否有shadow或block属性
            if (input.shadow) {
                return input.shadow;
            } else if (input.block) {
                return input.block;
            }
        } else if (typeof input === 'string') {
            return input;
        }
    } catch (e) {
        console.error('获取输入块出错:', e, block, inputName);
    }
    return null;
};

/**
 * 获取输入块的值
 * @param {string|Object} blockId - 输入块ID或块对象
 * @param {Object} blocks - 所有块
 * @returns {string} 输入值
 */
const getInputValue = (blockId, blocks) => {
    try {
        if (typeof blockId === 'string') {
            // 尝试多种方式获取块
            let block = null;
            if (blocks.getBlock) {
                block = blocks.getBlock(blockId);
            } else if (blocks._blocks && blocks._blocks[blockId]) {
                block = blocks._blocks[blockId];
            }

            if (!block) return '0';

            // 根据块类型获取适当的值
            if (block.opcode === 'math_number') {
                return block.fields && block.fields.NUM ? block.fields.NUM.value : '0';
            } else if (block.opcode === 'text') {
                return block.fields && block.fields.TEXT ? `"${block.fields.TEXT.value}"` : '""';
            } else if (block.opcode.startsWith('operator_')) {
                return getExpressionCode(blockId, blocks);
            } else if (block.opcode === 'sensing_answer') {
                return `"用户输入"`;
            } else if (block.opcode === 'sensing_timer') {
                return 'distsensor()';
            } else if (block.opcode === 'data_variable') {
                return block.fields && block.fields.VARIABLE ? block.fields.VARIABLE.value : 'var';
            } else if (block.opcode === 'k1_lightsensor_reporter') {
                return 'lightsensor()';
            } else if (block.opcode === 'k1_distsensor_reporter') {
                return 'distsensor()';
            } else if (block.opcode === 'k1_mic1sensor_reporter') {
                return 'mic1sensor()';
            } else if (block.opcode === 'k1_tracker_reporter') {
                const trackerId = getInputBlock(block, blocks, 'ID');
                const trackerIdValue = trackerId ? getInputValue(trackerId, blocks) : '1';
                return `tracker(${trackerIdValue})`;
            } else if (block.opcode === 'k1_get_ps2value_reporter') {
                return 'Get_Ps2Value()';
            } else if (block.opcode === 'math_positive_number') {
                return block.fields && block.fields.NUM ? block.fields.NUM.value : '1';
            } else {
                // 尝试从输入中获取数值
                // 检查是否有数字输入
                if (block.inputs) {
                    for (const inputName in block.inputs) {
                        const input = block.inputs[inputName];
                        if (input && (input.shadow || input.block)) {
                            const inputBlockId = input.shadow || input.block;
                            if (inputBlockId) {
                                const inputBlock = blocks._blocks[inputBlockId];
                                if (inputBlock && (
                                    inputBlock.opcode === 'math_number' ||
                                    inputBlock.opcode === 'math_positive_number' ||
                                    inputBlock.opcode === 'math_whole_number' ||
                                    inputBlock.opcode === 'math_integer' ||
                                    inputBlock.opcode === 'math_angle'
                                )) {
                                    return inputBlock.fields && inputBlock.fields.NUM ?
                                        inputBlock.fields.NUM.value : '1';
                                }
                            }
                        }
                    }
                }

                // 如果没有找到数字输入，返回默认值
                return '1';
            }
        } else if (typeof blockId === 'object') {
            // 直接传入了block对象
            const block = blockId;
            if (block.opcode === 'math_number') {
                return block.fields && block.fields.NUM ? block.fields.NUM.value : '0';
            } else if (block.opcode === 'math_positive_number') {
                return block.fields && block.fields.NUM ? block.fields.NUM.value : '1';
            } else if (block.opcode === 'math_whole_number') {
                return block.fields && block.fields.NUM ? block.fields.NUM.value : '0';
            } else if (block.opcode === 'math_integer') {
                return block.fields && block.fields.NUM ? block.fields.NUM.value : '0';
            } else if (block.opcode === 'math_angle') {
                return block.fields && block.fields.NUM ? block.fields.NUM.value : '0';
            } else if (block.opcode === 'text') {
                return block.fields && block.fields.TEXT ? `"${block.fields.TEXT.value}"` : '""';
            } else {
                return '0';
            }
        }
    } catch (e) {
        console.error('获取输入值出错:', e, blockId);
    }

    return '0';
};

/**
 * 获取表达式代码
 * @param {string} blockId - 块ID
 * @param {Object} blocks - 所有块
 * @returns {string} 表达式代码
 */
const getExpressionCode = (blockId, blocks) => {
    try {
        // 尝试多种方式获取块
        let block = null;
        if (blocks.getBlock) {
            block = blocks.getBlock(blockId);
        } else if (blocks._blocks && blocks._blocks[blockId]) {
            block = blocks._blocks[blockId];
        }

        if (!block) return 'true';

        switch (block.opcode) {
            case 'operator_equals':
            case 'operator_gt':
            case 'operator_lt':
                const opMap = {
                    'operator_equals': '==',
                    'operator_gt': '>',
                    'operator_lt': '<'
                };
                const op1 = getInputBlock(block, blocks, 'OPERAND1');
                const op2 = getInputBlock(block, blocks, 'OPERAND2');
                const op1Value = op1 ? getInputValue(op1, blocks) : '0';
                const op2Value = op2 ? getInputValue(op2, blocks) : '0';
                return `${op1Value} ${opMap[block.opcode]} ${op2Value}`;
            case 'operator_and':
                const and1 = getInputBlock(block, blocks, 'OPERAND1');
                const and2 = getInputBlock(block, blocks, 'OPERAND2');
                const and1Value = and1 ? getExpressionCode(and1, blocks) : 'true';
                const and2Value = and2 ? getExpressionCode(and2, blocks) : 'true';
                return `(${and1Value} && ${and2Value})`;
            case 'operator_or':
                const or1 = getInputBlock(block, blocks, 'OPERAND1');
                const or2 = getInputBlock(block, blocks, 'OPERAND2');
                const or1Value = or1 ? getExpressionCode(or1, blocks) : 'true';
                const or2Value = or2 ? getExpressionCode(or2, blocks) : 'true';
                return `(${or1Value} || ${or2Value})`;
            case 'operator_not':
                const notOp = getInputBlock(block, blocks, 'OPERAND');
                const notValue = notOp ? getExpressionCode(notOp, blocks) : 'true';
                return `!(${notValue})`;
            case 'sensing_timer':
                return 'distsensor()'; // 使用距离传感器代替计时器
            case 'sensing_loudness':
                return 'mic1sensor()'; // 使用噪声检测传感器
            case 'sensing_light':
                return 'lightsensor()'; // 使用光线传感器

            // K1传感器报告块
            case 'k1_lightsensor_reporter':
                return 'lightsensor()';
            case 'k1_distsensor_reporter':
                return 'distsensor()';
            case 'k1_mic1sensor_reporter':
                return 'mic1sensor()';
            case 'k1_tracker_reporter':
                const trackerReporterId = getInputBlock(block, blocks, 'ID');
                const trackerReporterIdValue = trackerReporterId ? getInputValue(trackerReporterId, blocks) : '1';
                return `tracker(${trackerReporterIdValue})`;
            case 'k1_get_ps2value_reporter':
                return 'Get_Ps2Value()';

            default:
                return 'true';
        }
    } catch (e) {
        console.error('获取表达式代码出错:', e, blockId);
        return 'true';
    }
};

export default generateCCodeFromBlocks;
