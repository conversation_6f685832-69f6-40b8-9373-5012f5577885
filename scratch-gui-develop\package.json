{"name": "scratch-gui", "version": "5.1.52", "description": "Graphical User Interface for creating and running Scratch 3.0 projects", "author": "Massachusetts Institute of Technology", "license": "AGPL-3.0-only", "homepage": "https://github.com/scratchfoundation/scratch-gui#readme", "repository": {"type": "git", "url": "https://github.com/scratchfoundation/scratch-gui.git"}, "main": "./dist/scratch-gui.js", "scripts": {"build": "npm run clean && webpack", "clean": "rimraf ./build ./dist", "deploy": "touch build/.nojekyll && gh-pages -t -d build -m \"[skip ci] Build for $(git log --pretty=format:%H -n1)\"", "prepublish": "node scripts/prepublish.mjs", "prune": "./prune-gh-pages.sh", "i18n:push": "tx-push-src scratch-editor interface translations/en.json", "i18n:src": "rimraf ./translations/messages/src && babel src > tmp.js && rimraf tmp.js && build-i18n-src ./translations/messages/src ./translations/", "start": "webpack serve", "test": "npm run test:lint && npm run test:unit && npm run build && npm run test:integration", "test:integration": "jest --maxWorkers=4 test[\\\\/]integration", "test:lint": "eslint . --ext .js,.jsx", "test:unit": "jest test[\\\\/]unit", "test:smoke": "jest --runInBand test[\\\\/]smoke", "watch": "webpack --watch"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "dependencies": {"@microbit/microbit-universal-hex": "^0.2.2", "arraybuffer-loader": "^1.0.6", "autoprefixer": "^9.0.1", "axios": "^1.9.0", "balance-text": "^3.3.1", "base64-loader": "^1.0.0", "bowser": "^1.9.4", "cat-blocks": "npm:scratch-blocks@0.1.0-prerelease.20220318143026", "classnames": "^2.2.6", "computed-style-to-inline-style": "^3.0.0", "cookie": "^0.6.0", "copy-webpack-plugin": "^6.4.1", "core-js": "^2.5.7", "css-loader": "5.2.7", "dapjs": "^2.3.0", "es6-object-assign": "^1.1.0", "fastestsmallesttextencoderdecoder": "^1.0.22", "get-float-time-domain-data": "^0.1.0", "get-user-media-promise": "^1.1.4", "highlight.js": "^11.11.1", "immutable": "^3.8.2", "intl": "^1.2.5", "js-base64": "^2.4.9", "keymirror": "^0.1.1", "lodash.bindall": "^4.4.0", "lodash.debounce": "^4.0.8", "lodash.defaultsdeep": "^4.6.1", "lodash.omit": "^4.5.0", "lodash.throttle": "^4.0.1", "minilog": "^3.1.0", "omggif": "^1.0.9", "papaparse": "^5.3.0", "path-browserify": "^1.0.1", "postcss-import": "^12.0.0", "postcss-loader": "4.3.0", "postcss-simple-vars": "^5.0.1", "prop-types": "^15.5.10", "query-string": "^5.1.1", "raw-loader": "^4.0.0", "react-contextmenu": "^2.9.4", "react-draggable": "^3.0.5", "react-ga": "^2.5.3", "react-intl": "^2.9.0", "react-modal": "^3.9.1", "react-popover": "^0.5.10", "react-redux": "^5.0.7", "react-responsive": "^5.0.0", "react-style-proptype": "^3.2.2", "react-syntax-highlighter": "^15.6.1", "react-tabs": "^2.3.0", "react-tooltip": "^4.5.1", "react-virtualized": "^9.20.1", "redux": "^3.7.2", "redux-throttle": "^0.1.1", "scratch-audio": "^2.0.0", "scratch-blocks": "^1.1.6", "scratch-l10n": "^5.0.0", "scratch-paint": "^3.0.0", "scratch-render": "^2.0.0", "scratch-render-fonts": "^1.0.2", "scratch-storage": "^4.0.0", "scratch-svg-renderer": "^3.0.0", "scratch-vm": "^5.0.0", "startaudiocontext": "^1.2.1", "style-loader": "4.0.0", "to-style": "^1.3.3", "util": "^0.12.5", "wav-encoder": "^1.3.0", "xhr": "^2.5.0"}, "peerDependencies": {"react": "^16.0.0", "react-dom": "^16.0.0"}, "devDependencies": {"@babel/cli": "7.27.0", "@babel/core": "7.26.10", "@babel/eslint-parser": "7.27.0", "@babel/plugin-proposal-object-rest-spread": "7.20.7", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-transform-async-to-generator": "7.25.9", "@babel/preset-env": "7.26.9", "@babel/preset-react": "7.26.3", "@commitlint/cli": "17.8.1", "@commitlint/config-conventional": "17.8.1", "babel-core": "7.0.0-bridge.0", "babel-loader": "9.2.1", "cheerio": "1.0.0-rc.3", "enzyme": "3.11.0", "enzyme-adapter-react-16": "1.15.8", "eslint": "8.57.1", "eslint-config-scratch": "9.0.9", "eslint-import-resolver-webpack": "0.11.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-jest": "22.21.0", "eslint-plugin-react": "7.37.4", "file-loader": "6.2.0", "gh-pages": "3.2.3", "html-webpack-plugin": "5.6.3", "husky": "8.0.3", "jest": "21.2.1", "jest-junit": "7.0.0", "raf": "3.4.1", "react-test-renderer": "16.14.0", "redux-mock-store": "1.5.5", "rimraf": "2.7.1", "scratch-semantic-release-config": "3.0.0", "scratch-webpack-configuration": "3.0.0", "selenium-webdriver": "3.6.0", "semantic-release": "19.0.5", "stream-browserify": "3.0.0", "url-loader": "4.1.1", "web-audio-test-api": "0.5.2", "webpack": "5.98.0", "webpack-cli": "5.1.4", "webpack-dev-server": "5.2.1", "yauzl": "2.10.0"}, "jest": {"setupFiles": ["raf/polyfill", "<rootDir>/test/helpers/enzyme-setup.js"], "testPathIgnorePatterns": ["src/test.js"], "moduleNameMapper": {"\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)\\??$": "<rootDir>/test/__mocks__/fileMock.js", "\\.(css|less)$": "<rootDir>/test/__mocks__/styleMock.js", "editor-msgs(\\.js)?$": "<rootDir>/test/__mocks__/editor-msgs-mock.js"}}}