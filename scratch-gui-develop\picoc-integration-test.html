<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scratch + PicoC 集成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .code-panel {
            flex: 1;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .output-panel {
            flex: 1;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        textarea {
            width: 100%;
            height: 300px;
            font-family: monospace;
            font-size: 14px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .sprite-area {
            width: 100%;
            height: 200px;
            border: 2px solid #333;
            position: relative;
            background-color: #e6f3ff;
            margin: 10px 0;
        }
        .sprite {
            width: 30px;
            height: 30px;
            background-color: #ff6b35;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
        }
        #output {
            background-color: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Scratch + PicoC 集成测试</h1>
    
    <div class="container">
        <div class="code-panel">
            <h2>C 代码编辑器</h2>
            <textarea id="codeEditor">#include <stdio.h>

int main() {
    printf("开始机器人程序\\n");
    
    // 电机运行示例
    printf("MOTOR_RUN:1:50\\n");  // 电机1，速度50
    printf("DELAY:1000\\n");      // 延时1秒
    
    printf("MOTOR_RUN:2:-30\\n"); // 电机2，速度-30（反向）
    printf("DELAY:500\\n");       // 延时0.5秒
    
    printf("MOTOR_STOP:1\\n");    // 停止电机1
    printf("MOTOR_STOP:2\\n");    // 停止电机2
    
    printf("程序执行完成\\n");
    return 0;
}</textarea>
            
            <div>
                <button id="runBtn" onclick="runCCode()">运行 C 代码</button>
                <button onclick="clearOutput()">清空输出</button>
                <button onclick="resetSprite()">重置精灵</button>
            </div>
            
            <div>
                <h3>精灵舞台</h3>
                <div class="sprite-area" id="spriteArea">
                    <div class="sprite" id="sprite"></div>
                </div>
                <div>
                    精灵位置: X=<span id="spriteX">0</span>, Y=<span id="spriteY">0</span>
                </div>
            </div>
        </div>
        
        <div class="output-panel">
            <h2>执行输出</h2>
            <div id="output"></div>
            
            <h3>命令解析</h3>
            <div id="commands"></div>
        </div>
    </div>

    <script src="https://unpkg.com/picoc-js@1.0.12/dist/bundle.umd.js"></script>
    <script>
        // 精灵控制器
        class SpriteController {
            constructor() {
                this.sprite = document.getElementById('sprite');
                this.spriteArea = document.getElementById('spriteArea');
                this.x = 0;
                this.y = 0;
                this.updatePosition();
            }
            
            updatePosition() {
                // 将逻辑坐标转换为屏幕坐标
                const areaRect = this.spriteArea.getBoundingClientRect();
                const centerX = areaRect.width / 2;
                const centerY = areaRect.height / 2;
                
                const screenX = centerX + this.x;
                const screenY = centerY - this.y; // Y轴翻转
                
                this.sprite.style.left = screenX + 'px';
                this.sprite.style.top = screenY + 'px';
                this.sprite.style.transform = 'translate(-50%, -50%)';
                
                // 更新显示
                document.getElementById('spriteX').textContent = this.x;
                document.getElementById('spriteY').textContent = this.y;
            }
            
            moveBy(dx, dy) {
                this.x += dx;
                this.y += dy;
                this.updatePosition();
            }
            
            moveTo(x, y) {
                this.x = x;
                this.y = y;
                this.updatePosition();
            }
            
            reset() {
                this.x = 0;
                this.y = 0;
                this.updatePosition();
            }
            
            motorRun(motor, speed) {
                console.log(`电机运行: 电机${motor}, 速度${speed}`);
                // 简单的电机到移动的映射
                if (motor === 1) {
                    // 电机1控制X轴移动
                    this.moveBy(speed / 5, 0);
                } else if (motor === 2) {
                    // 电机2控制Y轴移动
                    this.moveBy(0, speed / 5);
                }
            }
            
            motorStop(motor) {
                console.log(`电机停止: 电机${motor}`);
                // 这里可以添加停止动画的逻辑
            }
        }
        
        const spriteController = new SpriteController();
        
        function addOutput(text) {
            const output = document.getElementById('output');
            output.textContent += text;
            output.scrollTop = output.scrollHeight;
        }
        
        function addCommand(text) {
            const commands = document.getElementById('commands');
            const div = document.createElement('div');
            div.textContent = new Date().toLocaleTimeString() + ': ' + text;
            commands.appendChild(div);
            commands.scrollTop = commands.scrollHeight;
        }
        
        function parseAndExecuteCommands(output) {
            const lines = output.split('\n');
            
            lines.forEach(line => {
                line = line.trim();
                if (!line) return;
                
                if (line.startsWith('MOTOR_RUN:')) {
                    const parts = line.split(':');
                    if (parts.length === 3) {
                        const motor = parseInt(parts[1]);
                        const speed = parseInt(parts[2]);
                        spriteController.motorRun(motor, speed);
                        addCommand(`电机运行: 电机${motor}, 速度${speed}`);
                    }
                } else if (line.startsWith('MOTOR_STOP:')) {
                    const parts = line.split(':');
                    if (parts.length === 2) {
                        const motor = parseInt(parts[1]);
                        spriteController.motorStop(motor);
                        addCommand(`电机停止: 电机${motor}`);
                    }
                } else if (line.startsWith('DELAY:')) {
                    const parts = line.split(':');
                    if (parts.length === 2) {
                        const ms = parseInt(parts[1]);
                        addCommand(`延时: ${ms}ms`);
                        // 实际的延时会由C程序处理
                    }
                } else if (line === 'GET_PS2') {
                    addCommand('PS2手柄输入请求');
                } else if (line && !line.includes('开始') && !line.includes('完成')) {
                    addCommand(`输出: ${line}`);
                }
            });
        }
        
        async function runCCode() {
            const code = document.getElementById('codeEditor').value;
            const runBtn = document.getElementById('runBtn');
            
            if (!code.trim()) {
                alert('请输入C代码');
                return;
            }
            
            runBtn.disabled = true;
            runBtn.textContent = '执行中...';
            
            addOutput('=== 开始执行C代码 ===\n');
            
            try {
                await picocjs.runC(code, (output) => {
                    console.log('PicoC输出:', output);
                    addOutput(output);
                    parseAndExecuteCommands(output);
                });
                
                addOutput('\n=== C代码执行完成 ===\n');
                
            } catch (error) {
                console.error('执行失败:', error);
                addOutput('\n错误: ' + error.message + '\n');
            } finally {
                runBtn.disabled = false;
                runBtn.textContent = '运行 C 代码';
            }
        }
        
        function clearOutput() {
            document.getElementById('output').textContent = '';
            document.getElementById('commands').innerHTML = '';
        }
        
        function resetSprite() {
            spriteController.reset();
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            addOutput('PicoC + Scratch 集成测试环境已就绪\n');
            addCommand('系统初始化完成');
        });
    </script>
</body>
</html>
