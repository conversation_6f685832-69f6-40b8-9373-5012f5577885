<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>gpp_say 函数修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        textarea {
            width: 100%;
            height: 150px;
            font-family: monospace;
            font-size: 14px;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .sprite-area {
            width: 100%;
            height: 150px;
            border: 2px solid #333;
            position: relative;
            background-color: #e6f3ff;
            margin: 10px 0;
        }
        .sprite {
            width: 30px;
            height: 30px;
            background-color: #ff6b35;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
        }
        .speech-bubble {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 10px;
            padding: 5px 10px;
            font-size: 12px;
            display: none;
            z-index: 10;
        }
    </style>
</head>
<body>
    <h1>gpp_say 函数修复测试</h1>
    
    <div class="test-section info">
        <h2>测试说明</h2>
        <p>这个页面用于测试修复后的 gpp_say 函数和其他 K1 机器人函数。</p>
        <p>修复内容：</p>
        <ul>
            <li>✅ 添加了 gpp_say 函数的转换和处理</li>
            <li>✅ 添加了完整的 K1 机器人函数映射</li>
            <li>✅ 添加了中文翻译文件</li>
            <li>✅ 实现了精灵说话功能</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>测试 1: gpp_say 函数</h2>
        <textarea id="test1Code">#include <stdio.h>

int main() {
    gpp_say(1, "程序开始运行");
    gpp_say(0, "这是一个测试消息");
    gpp_say(1, "程序执行完成");
    return 0;
}</textarea>
        <button onclick="runTest(1)">运行测试 1</button>
        <div class="output" id="output1"></div>
    </div>

    <div class="test-section">
        <h2>测试 2: 综合 K1 函数</h2>
        <textarea id="test2Code">#include <stdio.h>

int main() {
    gpp_say(1, "开始机器人演示");
    
    forward(4, 10);
    gpp_say(1, "前进完成");
    
    turn_left(90);
    gpp_say(1, "左转完成");
    
    servo_open();
    gpp_say(1, "机械手打开");
    
    servo_close();
    gpp_say(1, "机械手关闭");
    
    gpp_say(1, "演示结束");
    return 0;
}</textarea>
        <button onclick="runTest(2)">运行测试 2</button>
        <div class="output" id="output2"></div>
    </div>

    <div class="test-section">
        <h2>精灵演示区</h2>
        <div class="sprite-area" id="spriteArea">
            <div class="sprite" id="sprite"></div>
            <div class="speech-bubble" id="speechBubble"></div>
        </div>
        <div>精灵位置: X=<span id="spriteX">0</span>, Y=<span id="spriteY">0</span></div>
        <button onclick="resetSprite()">重置精灵</button>
    </div>

    <script src="https://unpkg.com/picoc-js@1.0.12/dist/bundle.umd.js"></script>
    <script>
        // 精灵控制器
        class SpriteController {
            constructor() {
                this.sprite = document.getElementById('sprite');
                this.spriteArea = document.getElementById('spriteArea');
                this.speechBubble = document.getElementById('speechBubble');
                this.x = 0;
                this.y = 0;
                this.direction = 90; // 默认向上
                this.updatePosition();
            }
            
            updatePosition() {
                const areaRect = this.spriteArea.getBoundingClientRect();
                const centerX = areaRect.width / 2;
                const centerY = areaRect.height / 2;
                
                const screenX = centerX + this.x;
                const screenY = centerY - this.y;
                
                this.sprite.style.left = screenX + 'px';
                this.sprite.style.top = screenY + 'px';
                this.sprite.style.transform = 'translate(-50%, -50%)';
                
                document.getElementById('spriteX').textContent = this.x;
                document.getElementById('spriteY').textContent = this.y;
            }
            
            say(text, mode = 1) {
                console.log(`精灵说话: ${text} (模式: ${mode})`);
                this.speechBubble.textContent = text;
                this.speechBubble.style.display = 'block';
                
                // 定位对话框
                const spriteRect = this.sprite.getBoundingClientRect();
                const areaRect = this.spriteArea.getBoundingClientRect();
                this.speechBubble.style.left = (spriteRect.left - areaRect.left + 35) + 'px';
                this.speechBubble.style.top = (spriteRect.top - areaRect.top - 30) + 'px';
                
                // 根据模式设置清除时间
                const clearTime = mode === 0 ? 2000 : 3000;
                setTimeout(() => {
                    this.speechBubble.style.display = 'none';
                }, clearTime);
            }
            
            forward(speed, distance) {
                const moveDistance = distance * (speed / 4);
                const radians = this.direction * Math.PI / 180;
                const dx = moveDistance * Math.sin(radians);
                const dy = moveDistance * Math.cos(radians);
                this.x += dx;
                this.y += dy;
                this.updatePosition();
            }
            
            turnLeft(degrees) {
                this.direction -= degrees;
                console.log(`精灵左转 ${degrees} 度，当前方向: ${this.direction}`);
            }
            
            turnRight(degrees) {
                this.direction += degrees;
                console.log(`精灵右转 ${degrees} 度，当前方向: ${this.direction}`);
            }
            
            reset() {
                this.x = 0;
                this.y = 0;
                this.direction = 90;
                this.updatePosition();
                this.speechBubble.style.display = 'none';
            }
        }
        
        const spriteController = new SpriteController();
        
        // 模拟 Scratch VM 控制器
        window.scratchVMController = {
            say: (mode, text) => {
                const cleanText = text.replace(/['"]/g, '');
                spriteController.say(cleanText, mode);
            },
            forward: (speed, distance) => {
                spriteController.forward(speed, distance);
            },
            turnLeft: (degrees) => {
                spriteController.turnLeft(degrees);
            },
            turnRight: (degrees) => {
                spriteController.turnRight(degrees);
            },
            servoOpen: () => {
                spriteController.say('机械手打开', 1);
            },
            servoClose: () => {
                spriteController.say('机械手关闭', 1);
            }
        };
        
        function addOutput(testNum, text) {
            const output = document.getElementById(`output${testNum}`);
            output.textContent += text;
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput(testNum) {
            document.getElementById(`output${testNum}`).textContent = '';
        }
        
        function parseAndExecuteCommands(output, testNum) {
            const lines = output.split('\n');
            
            lines.forEach(line => {
                line = line.trim();
                if (!line) return;
                
                addOutput(testNum, `解析: ${line}\n`);
                
                if (line.startsWith('GPP_SAY:')) {
                    const parts = line.split(':');
                    if (parts.length >= 3) {
                        const mode = parseInt(parts[1]);
                        const text = parts.slice(2).join(':');
                        window.scratchVMController.say(mode, text);
                    }
                } else if (line.startsWith('FORWARD:')) {
                    const parts = line.split(':');
                    if (parts.length === 3) {
                        const speed = parseInt(parts[1]);
                        const distance = parseInt(parts[2]);
                        window.scratchVMController.forward(speed, distance);
                    }
                } else if (line.startsWith('TURN_LEFT:')) {
                    const parts = line.split(':');
                    if (parts.length === 2) {
                        const degrees = parseInt(parts[1]);
                        window.scratchVMController.turnLeft(degrees);
                    }
                } else if (line === 'SERVO_OPEN') {
                    window.scratchVMController.servoOpen();
                } else if (line === 'SERVO_CLOSE') {
                    window.scratchVMController.servoClose();
                }
            });
        }
        
        async function runTest(testNum) {
            const code = document.getElementById(`test${testNum}Code`).value;
            clearOutput(testNum);
            
            addOutput(testNum, '=== 开始执行测试 ===\n');
            
            // 转换代码
            let modifiedCode = code;
            
            // 应用与主程序相同的转换
            modifiedCode = modifiedCode.replace(/gpp_say\s*\(\s*(\d+)\s*,\s*([^)]+)\s*\)/g, 
                'printf("GPP_SAY:%d:%s\\n", $1, $2)');
            modifiedCode = modifiedCode.replace(/forward\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)/g, 
                'printf("FORWARD:%d:%d\\n", $1, $2)');
            modifiedCode = modifiedCode.replace(/turn_left\s*\(\s*(\d+)\s*\)/g, 
                'printf("TURN_LEFT:%d\\n", $1)');
            modifiedCode = modifiedCode.replace(/servo_open\s*\(\s*\)/g, 
                'printf("SERVO_OPEN\\n")');
            modifiedCode = modifiedCode.replace(/servo_close\s*\(\s*\)/g, 
                'printf("SERVO_CLOSE\\n")');
            
            addOutput(testNum, '转换后的代码:\n' + modifiedCode + '\n\n');
            
            try {
                await picocjs.runC(modifiedCode, (output) => {
                    addOutput(testNum, `输出: ${output}`);
                    parseAndExecuteCommands(output, testNum);
                });
                
                addOutput(testNum, '\n=== 测试完成 ===\n');
                
            } catch (error) {
                addOutput(testNum, `\n错误: ${error.message}\n`);
            }
        }
        
        function resetSprite() {
            spriteController.reset();
        }
        
        // 页面加载完成后显示状态
        window.addEventListener('load', () => {
            console.log('gpp_say 修复测试页面加载完成');
        });
    </script>
</body>
</html>
