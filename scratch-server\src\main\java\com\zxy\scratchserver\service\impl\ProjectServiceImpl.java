package com.zxy.scratchserver.service.impl;

import com.zxy.scratchserver.dto.ProjectDetailResponse;
import com.zxy.scratchserver.dto.ProjectResponse;
import com.zxy.scratchserver.dto.SaveProjectRequest;
import com.zxy.scratchserver.dto.VersionResponse;
import com.zxy.scratchserver.model.Project;
import com.zxy.scratchserver.model.User;
import com.zxy.scratchserver.model.Version;
import com.zxy.scratchserver.repository.ProjectRepository;
import com.zxy.scratchserver.repository.UserRepository;
import com.zxy.scratchserver.repository.VersionRepository;
import com.zxy.scratchserver.service.ProjectService;
import com.zxy.scratchserver.service.VersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目服务实现类
 */
@Service
public class ProjectServiceImpl implements ProjectService {

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private VersionRepository versionRepository;

    @Autowired
    private VersionService versionService;

    @Override
    public List<ProjectResponse> getUserProjects(String username) {
        // 获取用户信息
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 获取用户的项目列表
        List<Project> projects = projectRepository.findByUserId(user.getId());

        // 转换为响应DTO
        return projects.stream()
                .map(project -> {
                    // 获取项目的最新版本号
                    String versionNumber = getLatestVersionNumber(project.getId());

                    return ProjectResponse.builder()
                            .projectId(project.getId())
                            .userId(project.getUserId())
                            .projectName(project.getProjectName())
                            .projectCreatedAt(project.getCreatedAt())
                            .username(username)
                            .versionNumber(versionNumber)
                            .build();
                })
                .collect(Collectors.toList());
    }

    @Override
    public ProjectDetailResponse getProjectDetail(Long projectId, String username) {
        // 获取用户信息
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 获取项目详情
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("项目不存在"));

        // 检查项目是否属于当前用户
        if (!project.getUserId().equals(user.getId())) {
            throw new RuntimeException("无权访问该项目");
        }

        // 转换为响应DTO
        return ProjectDetailResponse.builder()
                .projectId(project.getId())
                .userId(project.getUserId())
                .projectName(project.getProjectName())
                .projectBlocks(project.getProjectBlocks())
                .projectCreatedAt(project.getCreatedAt())
                .username(username)
                .build();
    }

    @Override
    public Long saveProject(SaveProjectRequest saveProjectRequest, String username) {
        // 获取用户信息
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 打印项目数据大小
        byte[] projectBlocks = saveProjectRequest.getProjectBlocks();
        if (projectBlocks != null) {
            System.out.println("项目数据大小: " + projectBlocks.length + " 字节 (" + (projectBlocks.length / 1024.0 / 1024.0) + " MB)");

            // 检查数据大小是否超过16MB（mediumblob的限制）
            if (projectBlocks.length > 16 * 1024 * 1024) {
                System.out.println("警告：项目数据大小超过16MB，可能需要修改数据库表结构");
            }
        }

        Project project;

        // 检查是否有项目ID，如果有则更新现有项目，否则创建新项目
        if (saveProjectRequest.getProjectId() != null) {
            // 尝试获取现有项目
            project = projectRepository.findById(saveProjectRequest.getProjectId())
                    .orElseThrow(() -> new RuntimeException("项目不存在，ID: " + saveProjectRequest.getProjectId()));

            // 检查项目是否属于当前用户
            if (!project.getUserId().equals(user.getId())) {
                throw new RuntimeException("无权修改该项目");
            }

            // 更新项目信息
            project.setProjectName(saveProjectRequest.getProjectName());
            project.setProjectBlocks(saveProjectRequest.getProjectBlocks());

            System.out.println("更新现有项目，ID: " + project.getId());
        } else {
            // 创建新项目
            project = new Project();
            project.setUserId(user.getId());
            project.setProjectName(saveProjectRequest.getProjectName());
            project.setProjectBlocks(saveProjectRequest.getProjectBlocks());

            System.out.println("创建新项目");
        }

        try {
            // 保存项目
            Project savedProject = projectRepository.save(project);
            System.out.println("项目保存成功，ID: " + savedProject.getId());

            // 注意：不再自动创建版本，由前端调用版本创建API
            // 这样可以避免创建重复的版本记录

            return savedProject.getId();
        } catch (Exception e) {
            System.err.println("保存项目失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    @Override
    public String getLatestVersionNumber(Long projectId) {
        VersionResponse latestVersion = versionService.getLatestVersion(projectId);
        return latestVersion != null ? latestVersion.getVersionNumber() : "v1.0";
    }

    @Override
    public VersionResponse createProjectVersion(Long projectId, String username) {
        // 获取用户信息
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 更新项目版本号，而不是创建新版本
        return versionService.updateVersionNumber(projectId, user.getId());
    }

    @Override
    public boolean deleteProject(Long projectId, String username) {
        try {
            // 获取用户信息
            User user = userRepository.findByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            // 获取项目
            Project project = projectRepository.findById(projectId)
                    .orElseThrow(() -> new RuntimeException("项目不存在"));

            // 检查项目是否属于当前用户
            if (!project.getUserId().equals(user.getId())) {
                throw new RuntimeException("无权删除该项目");
            }

            // 先删除项目相关的版本记录
            List<Version> versions = versionRepository.findByProjectId(projectId);
            if (!versions.isEmpty()) {
                System.out.println("删除项目相关的版本记录，数量: " + versions.size());
                versionRepository.deleteAll(versions);
            }

            // 删除项目
            System.out.println("删除项目，ID: " + projectId);
            projectRepository.deleteById(projectId);

            return true;
        } catch (Exception e) {
            System.err.println("删除项目失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
}
