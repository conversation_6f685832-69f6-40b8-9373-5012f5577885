(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('fs'), require('path'), require('crypto')) :
	typeof define === 'function' && define.amd ? define(['exports', 'fs', 'path', 'crypto'], factory) :
	(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.picocjs = {}, global.require$$0, global.require$$1, global.require$$2));
})(this, (function (exports, require$$0, require$$1, require$$2) { 'use strict';

	function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

	var require$$0__default = /*#__PURE__*/_interopDefaultLegacy(require$$0);
	var require$$1__default = /*#__PURE__*/_interopDefaultLegacy(require$$1);
	var require$$2__default = /*#__PURE__*/_interopDefaultLegacy(require$$2);

	function createCommonjsModule(fn) {
	  var module = { exports: {} };
		return fn(module, module.exports), module.exports;
	}

	var picoc = createCommonjsModule(function (module, exports) {
	var PicocModule = (function() {
	  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
	  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;
	  return (
	function(PicocModule) {
	  PicocModule = PicocModule || {};

	// Copyright 2010 The Emscripten Authors.  All rights reserved.
	// Emscripten is available under two separate licenses, the MIT license and the
	// University of Illinois/NCSA Open Source License.  Both these licenses can be
	// found in the LICENSE file.

	// The Module object: Our interface to the outside world. We import
	// and export values on it. There are various ways Module can be used:
	// 1. Not defined. We create it here
	// 2. A function parameter, function(Module) { ..generated code.. }
	// 3. pre-run appended it, var Module = {}; ..generated code..
	// 4. External script tag defines var Module.
	// We need to check if Module already exists (e.g. case 3 above).
	// Substitution will be replaced with actual code on later stage of the build,
	// this way Closure Compiler will not mangle it (e.g. case 4. above).
	// Note that if you want to run closure, and also to use Module
	// after the generated code, you will need to define   var Module = {};
	// before the code. Then that object will be used in the code, and you
	// can continue to use Module afterwards as well.
	var Module = typeof PicocModule !== 'undefined' ? PicocModule : {};

	// --pre-jses are emitted after the Module integration code, so that they can
	// refer to Module (if they choose; they can also define Module)
	Module['noInitialRun'] = true;
	const __dirname = "";
	Module['print'] = (a) => { 
	  let f = Module['consoleWrite'] || console.log.bind(console);
	  f(a);
	};



	// Sometimes an existing Module object exists with properties
	// meant to overwrite the default module functionality. Here
	// we collect those properties and reapply _after_ we configure
	// the current environment's defaults to avoid having to be so
	// defensive during initialization.
	var moduleOverrides = {};
	var key;
	for (key in Module) {
	  if (Module.hasOwnProperty(key)) {
	    moduleOverrides[key] = Module[key];
	  }
	}

	var arguments_ = [];
	var thisProgram = './this.program';
	var quit_ = function(status, toThrow) {
	  throw toThrow;
	};

	// Determine the runtime environment we are in. You can customize this by
	// setting the ENVIRONMENT setting at compile time (see settings.js).

	var ENVIRONMENT_IS_WEB = false;
	var ENVIRONMENT_IS_WORKER = false;
	var ENVIRONMENT_IS_NODE = false;
	var ENVIRONMENT_HAS_NODE = false;
	var ENVIRONMENT_IS_SHELL = false;
	ENVIRONMENT_IS_WEB = typeof window === 'object';
	ENVIRONMENT_IS_WORKER = typeof importScripts === 'function';
	// A web environment like Electron.js can have Node enabled, so we must
	// distinguish between Node-enabled environments and Node environments per se.
	// This will allow the former to do things like mount NODEFS.
	// Extended check using process.versions fixes issue #8816.
	// (Also makes redundant the original check that 'require' is a function.)
	ENVIRONMENT_HAS_NODE = typeof process === 'object' && typeof process.versions === 'object' && typeof process.versions.node === 'string';
	ENVIRONMENT_IS_NODE = ENVIRONMENT_HAS_NODE && !ENVIRONMENT_IS_WEB && !ENVIRONMENT_IS_WORKER;
	ENVIRONMENT_IS_SHELL = !ENVIRONMENT_IS_WEB && !ENVIRONMENT_IS_NODE && !ENVIRONMENT_IS_WORKER;




	// `/` should be present at the end if `scriptDirectory` is not empty
	var scriptDirectory = '';
	function locateFile(path) {
	  if (Module['locateFile']) {
	    return Module['locateFile'](path, scriptDirectory);
	  }
	  return scriptDirectory + path;
	}

	// Hooks that are implemented differently in different runtime environments.
	var read_,
	    readBinary;


	var nodeFS;
	var nodePath;

	if (ENVIRONMENT_IS_NODE) {
	  scriptDirectory = __dirname + '/';


	  read_ = function shell_read(filename, binary) {
	    var ret = tryParseAsDataURI(filename);
	    if (ret) {
	      return binary ? ret : ret.toString();
	    }
	    if (!nodeFS) nodeFS = require$$0__default["default"];
	    if (!nodePath) nodePath = require$$1__default["default"];
	    filename = nodePath['normalize'](filename);
	    return nodeFS['readFileSync'](filename, binary ? null : 'utf8');
	  };

	  readBinary = function readBinary(filename) {
	    var ret = read_(filename, true);
	    if (!ret.buffer) {
	      ret = new Uint8Array(ret);
	    }
	    assert(ret.buffer);
	    return ret;
	  };




	  if (process['argv'].length > 1) {
	    thisProgram = process['argv'][1].replace(/\\/g, '/');
	  }

	  arguments_ = process['argv'].slice(2);

	  // MODULARIZE will export the module in the proper place outside, we don't need to export here

	  process['on']('uncaughtException', function(ex) {
	    // suppress ExitStatus exceptions from showing an error
	    if (!(ex instanceof ExitStatus)) {
	      throw ex;
	    }
	  });

	  process['on']('unhandledRejection', abort);

	  quit_ = function(status) {
	    process['exit'](status);
	  };

	  Module['inspect'] = function () { return '[Emscripten Module object]'; };



	} else
	if (ENVIRONMENT_IS_SHELL) {


	  if (typeof read != 'undefined') {
	    read_ = function shell_read(f) {
	      var data = tryParseAsDataURI(f);
	      if (data) {
	        return intArrayToString(data);
	      }
	      return read(f);
	    };
	  }

	  readBinary = function readBinary(f) {
	    var data;
	    data = tryParseAsDataURI(f);
	    if (data) {
	      return data;
	    }
	    if (typeof readbuffer === 'function') {
	      return new Uint8Array(readbuffer(f));
	    }
	    data = read(f, 'binary');
	    assert(typeof data === 'object');
	    return data;
	  };

	  if (typeof scriptArgs != 'undefined') {
	    arguments_ = scriptArgs;
	  } else if (typeof arguments != 'undefined') {
	    arguments_ = arguments;
	  }

	  if (typeof quit === 'function') {
	    quit_ = function(status) {
	      quit(status);
	    };
	  }

	  if (typeof print !== 'undefined') {
	    // Prefer to use print/printErr where they exist, as they usually work better.
	    if (typeof console === 'undefined') console = {};
	    console.log = print;
	    console.warn = console.error = typeof printErr !== 'undefined' ? printErr : print;
	  }


	} else

	// Note that this includes Node.js workers when relevant (pthreads is enabled).
	// Node.js workers are detected as a combination of ENVIRONMENT_IS_WORKER and
	// ENVIRONMENT_HAS_NODE.
	if (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) {
	  if (ENVIRONMENT_IS_WORKER) { // Check worker, not web, since window could be polyfilled
	    scriptDirectory = self.location.href;
	  } else if (document.currentScript) { // web
	    scriptDirectory = document.currentScript.src;
	  }
	  // When MODULARIZE (and not _INSTANCE), this JS may be executed later, after document.currentScript
	  // is gone, so we saved it, and we use it here instead of any other info.
	  if (_scriptDir) {
	    scriptDirectory = _scriptDir;
	  }
	  // blob urls look like blob:http://site.com/etc/etc and we cannot infer anything from them.
	  // otherwise, slice off the final part of the url to find the script directory.
	  // if scriptDirectory does not contain a slash, lastIndexOf will return -1,
	  // and scriptDirectory will correctly be replaced with an empty string.
	  if (scriptDirectory.indexOf('blob:') !== 0) {
	    scriptDirectory = scriptDirectory.substr(0, scriptDirectory.lastIndexOf('/')+1);
	  } else {
	    scriptDirectory = '';
	  }


	  // Differentiate the Web Worker from the Node Worker case, as reading must
	  // be done differently.
	  {


	  read_ = function shell_read(url) {
	    try {
	      var xhr = new XMLHttpRequest();
	      xhr.open('GET', url, false);
	      xhr.send(null);
	      return xhr.responseText;
	    } catch (err) {
	      var data = tryParseAsDataURI(url);
	      if (data) {
	        return intArrayToString(data);
	      }
	      throw err;
	    }
	  };

	  if (ENVIRONMENT_IS_WORKER) {
	    readBinary = function readBinary(url) {
	      try {
	        var xhr = new XMLHttpRequest();
	        xhr.open('GET', url, false);
	        xhr.responseType = 'arraybuffer';
	        xhr.send(null);
	        return new Uint8Array(xhr.response);
	      } catch (err) {
	        var data = tryParseAsDataURI(url);
	        if (data) {
	          return data;
	        }
	        throw err;
	      }
	    };
	  }




	  }
	} else
	;


	// Set up the out() and err() hooks, which are how we can print to stdout or
	// stderr, respectively.
	var out = Module['print'] || console.log.bind(console);
	var err = Module['printErr'] || console.warn.bind(console);

	// Merge back in the overrides
	for (key in moduleOverrides) {
	  if (moduleOverrides.hasOwnProperty(key)) {
	    Module[key] = moduleOverrides[key];
	  }
	}
	// Free the object hierarchy contained in the overrides, this lets the GC
	// reclaim data used e.g. in memoryInitializerRequest, which is a large typed array.
	moduleOverrides = null;

	// Emit code to handle expected values on the Module object. This applies Module.x
	// to the proper local x. This has two benefits: first, we only emit it if it is
	// expected to arrive, and second, by using a local everywhere else that can be
	// minified.
	if (Module['arguments']) arguments_ = Module['arguments'];
	if (Module['thisProgram']) thisProgram = Module['thisProgram'];
	if (Module['quit']) quit_ = Module['quit'];


	function dynamicAlloc(size) {
	  var ret = HEAP32[DYNAMICTOP_PTR>>2];
	  var end = (ret + size + 15) & -16;
	  if (end > _emscripten_get_heap_size()) {
	    abort();
	  }
	  HEAP32[DYNAMICTOP_PTR>>2] = end;
	  return ret;
	}

	function getNativeTypeSize(type) {
	  switch (type) {
	    case 'i1': case 'i8': return 1;
	    case 'i16': return 2;
	    case 'i32': return 4;
	    case 'i64': return 8;
	    case 'float': return 4;
	    case 'double': return 8;
	    default: {
	      if (type[type.length-1] === '*') {
	        return 4; // A pointer
	      } else if (type[0] === 'i') {
	        var bits = parseInt(type.substr(1));
	        assert(bits % 8 === 0, 'getNativeTypeSize invalid bits ' + bits + ', type ' + type);
	        return bits / 8;
	      } else {
	        return 0;
	      }
	    }
	  }
	}

	var tempRet0 = 0;

	var setTempRet0 = function(value) {
	  tempRet0 = value;
	};

	var getTempRet0 = function() {
	  return tempRet0;
	};




	// === Preamble library stuff ===

	// Documentation for the public APIs defined in this file must be updated in:
	//    site/source/docs/api_reference/preamble.js.rst
	// A prebuilt local version of the documentation is available at:
	//    site/build/text/docs/api_reference/preamble.js.txt
	// You can also build docs locally as HTML or other formats in site/
	// An online HTML version (which may be of a different version of Emscripten)
	//    is up at http://kripken.github.io/emscripten-site/docs/api_reference/preamble.js.html


	var wasmBinary;if (Module['wasmBinary']) wasmBinary = Module['wasmBinary'];
	var noExitRuntime;if (Module['noExitRuntime']) noExitRuntime = Module['noExitRuntime'];


	if (typeof WebAssembly !== 'object') {
	  err('no native wasm support detected');
	}


	// In MINIMAL_RUNTIME, setValue() and getValue() are only available when building with safe heap enabled, for heap safety checking.
	// In traditional runtime, setValue() and getValue() are always available (although their use is highly discouraged due to perf penalties)

	/** @type {function(number, number, string, boolean=)} */
	function setValue(ptr, value, type, noSafe) {
	  type = type || 'i8';
	  if (type.charAt(type.length-1) === '*') type = 'i32'; // pointers are 32-bit
	    switch(type) {
	      case 'i1': HEAP8[((ptr)>>0)]=value; break;
	      case 'i8': HEAP8[((ptr)>>0)]=value; break;
	      case 'i16': HEAP16[((ptr)>>1)]=value; break;
	      case 'i32': HEAP32[((ptr)>>2)]=value; break;
	      case 'i64': (tempI64 = [value>>>0,(tempDouble=value,(+(Math_abs(tempDouble))) >= 1.0 ? (tempDouble > 0.0 ? ((Math_min((+(Math_floor((tempDouble)/4294967296.0))), 4294967295.0))|0)>>>0 : (~~((+(Math_ceil((tempDouble - +(((~~(tempDouble)))>>>0))/4294967296.0)))))>>>0) : 0)],HEAP32[((ptr)>>2)]=tempI64[0],HEAP32[(((ptr)+(4))>>2)]=tempI64[1]); break;
	      case 'float': HEAPF32[((ptr)>>2)]=value; break;
	      case 'double': HEAPF64[((ptr)>>3)]=value; break;
	      default: abort('invalid type for setValue: ' + type);
	    }
	}





	// Wasm globals

	var wasmMemory;

	// In fastcomp asm.js, we don't need a wasm Table at all.
	// In the wasm backend, we polyfill the WebAssembly object,
	// so this creates a (non-native-wasm) table for us.
	var wasmTable = new WebAssembly.Table({
	  'initial': 248,
	  'maximum': 248 + 0,
	  'element': 'anyfunc'
	});


	//========================================
	// Runtime essentials
	//========================================

	// whether we are quitting the application. no code should run after this.
	// set in exit() and abort()
	var ABORT = false;

	/** @type {function(*, string=)} */
	function assert(condition, text) {
	  if (!condition) {
	    abort('Assertion failed: ' + text);
	  }
	}

	var ALLOC_NORMAL = 0; // Tries to use _malloc()
	var ALLOC_NONE = 3; // Do not allocate

	// allocate(): This is for internal use. You can use it yourself as well, but the interface
	//             is a little tricky (see docs right below). The reason is that it is optimized
	//             for multiple syntaxes to save space in generated code. So you should
	//             normally not use allocate(), and instead allocate memory using _malloc(),
	//             initialize it with setValue(), and so forth.
	// @slab: An array of data, or a number. If a number, then the size of the block to allocate,
	//        in *bytes* (note that this is sometimes confusing: the next parameter does not
	//        affect this!)
	// @types: Either an array of types, one for each byte (or 0 if no type at that position),
	//         or a single type which is used for the entire block. This only matters if there
	//         is initial data - if @slab is a number, then this does not matter at all and is
	//         ignored.
	// @allocator: How to allocate memory, see ALLOC_*
	/** @type {function((TypedArray|Array<number>|number), string, number, number=)} */
	function allocate(slab, types, allocator, ptr) {
	  var zeroinit, size;
	  if (typeof slab === 'number') {
	    zeroinit = true;
	    size = slab;
	  } else {
	    zeroinit = false;
	    size = slab.length;
	  }

	  var singleType = typeof types === 'string' ? types : null;

	  var ret;
	  if (allocator == ALLOC_NONE) {
	    ret = ptr;
	  } else {
	    ret = [_malloc,
	    stackAlloc,
	    dynamicAlloc][allocator](Math.max(size, singleType ? 1 : types.length));
	  }

	  if (zeroinit) {
	    var stop;
	    ptr = ret;
	    assert((ret & 3) == 0);
	    stop = ret + (size & ~3);
	    for (; ptr < stop; ptr += 4) {
	      HEAP32[((ptr)>>2)]=0;
	    }
	    stop = ret + size;
	    while (ptr < stop) {
	      HEAP8[((ptr++)>>0)]=0;
	    }
	    return ret;
	  }

	  if (singleType === 'i8') {
	    if (slab.subarray || slab.slice) {
	      HEAPU8.set(/** @type {!Uint8Array} */ (slab), ret);
	    } else {
	      HEAPU8.set(new Uint8Array(slab), ret);
	    }
	    return ret;
	  }

	  var i = 0, type, typeSize, previousType;
	  while (i < size) {
	    var curr = slab[i];

	    type = singleType || types[i];
	    if (type === 0) {
	      i++;
	      continue;
	    }

	    if (type == 'i64') type = 'i32'; // special case: we have one i32 here, and one i32 later

	    setValue(ret+i, curr, type);

	    // no need to look up size unless type changes, so cache it
	    if (previousType !== type) {
	      typeSize = getNativeTypeSize(type);
	      previousType = type;
	    }
	    i += typeSize;
	  }

	  return ret;
	}


	// runtime_strings.js: Strings related runtime functions that are part of both MINIMAL_RUNTIME and regular runtime.

	// Given a pointer 'ptr' to a null-terminated UTF8-encoded string in the given array that contains uint8 values, returns
	// a copy of that string as a Javascript String object.

	var UTF8Decoder = typeof TextDecoder !== 'undefined' ? new TextDecoder('utf8') : undefined;

	/**
	 * @param {number} idx
	 * @param {number=} maxBytesToRead
	 * @return {string}
	 */
	function UTF8ArrayToString(u8Array, idx, maxBytesToRead) {
	  var endIdx = idx + maxBytesToRead;
	  var endPtr = idx;
	  // TextDecoder needs to know the byte length in advance, it doesn't stop on null terminator by itself.
	  // Also, use the length info to avoid running tiny strings through TextDecoder, since .subarray() allocates garbage.
	  // (As a tiny code save trick, compare endPtr against endIdx using a negation, so that undefined means Infinity)
	  while (u8Array[endPtr] && !(endPtr >= endIdx)) ++endPtr;

	  if (endPtr - idx > 16 && u8Array.subarray && UTF8Decoder) {
	    return UTF8Decoder.decode(u8Array.subarray(idx, endPtr));
	  } else {
	    var str = '';
	    // If building with TextDecoder, we have already computed the string length above, so test loop end condition against that
	    while (idx < endPtr) {
	      // For UTF8 byte structure, see:
	      // http://en.wikipedia.org/wiki/UTF-8#Description
	      // https://www.ietf.org/rfc/rfc2279.txt
	      // https://tools.ietf.org/html/rfc3629
	      var u0 = u8Array[idx++];
	      if (!(u0 & 0x80)) { str += String.fromCharCode(u0); continue; }
	      var u1 = u8Array[idx++] & 63;
	      if ((u0 & 0xE0) == 0xC0) { str += String.fromCharCode(((u0 & 31) << 6) | u1); continue; }
	      var u2 = u8Array[idx++] & 63;
	      if ((u0 & 0xF0) == 0xE0) {
	        u0 = ((u0 & 15) << 12) | (u1 << 6) | u2;
	      } else {
	        u0 = ((u0 & 7) << 18) | (u1 << 12) | (u2 << 6) | (u8Array[idx++] & 63);
	      }

	      if (u0 < 0x10000) {
	        str += String.fromCharCode(u0);
	      } else {
	        var ch = u0 - 0x10000;
	        str += String.fromCharCode(0xD800 | (ch >> 10), 0xDC00 | (ch & 0x3FF));
	      }
	    }
	  }
	  return str;
	}

	// Given a pointer 'ptr' to a null-terminated UTF8-encoded string in the emscripten HEAP, returns a
	// copy of that string as a Javascript String object.
	// maxBytesToRead: an optional length that specifies the maximum number of bytes to read. You can omit
	//                 this parameter to scan the string until the first \0 byte. If maxBytesToRead is
	//                 passed, and the string at [ptr, ptr+maxBytesToReadr[ contains a null byte in the
	//                 middle, then the string will cut short at that byte index (i.e. maxBytesToRead will
	//                 not produce a string of exact length [ptr, ptr+maxBytesToRead[)
	//                 N.B. mixing frequent uses of UTF8ToString() with and without maxBytesToRead may
	//                 throw JS JIT optimizations off, so it is worth to consider consistently using one
	//                 style or the other.
	/**
	 * @param {number} ptr
	 * @param {number=} maxBytesToRead
	 * @return {string}
	 */
	function UTF8ToString(ptr, maxBytesToRead) {
	  return ptr ? UTF8ArrayToString(HEAPU8, ptr, maxBytesToRead) : '';
	}

	// Copies the given Javascript String object 'str' to the given byte array at address 'outIdx',
	// encoded in UTF8 form and null-terminated. The copy will require at most str.length*4+1 bytes of space in the HEAP.
	// Use the function lengthBytesUTF8 to compute the exact number of bytes (excluding null terminator) that this function will write.
	// Parameters:
	//   str: the Javascript string to copy.
	//   outU8Array: the array to copy to. Each index in this array is assumed to be one 8-byte element.
	//   outIdx: The starting offset in the array to begin the copying.
	//   maxBytesToWrite: The maximum number of bytes this function can write to the array.
	//                    This count should include the null terminator,
	//                    i.e. if maxBytesToWrite=1, only the null terminator will be written and nothing else.
	//                    maxBytesToWrite=0 does not write any bytes to the output, not even the null terminator.
	// Returns the number of bytes written, EXCLUDING the null terminator.

	function stringToUTF8Array(str, outU8Array, outIdx, maxBytesToWrite) {
	  if (!(maxBytesToWrite > 0)) // Parameter maxBytesToWrite is not optional. Negative values, 0, null, undefined and false each don't write out any bytes.
	    return 0;

	  var startIdx = outIdx;
	  var endIdx = outIdx + maxBytesToWrite - 1; // -1 for string null terminator.
	  for (var i = 0; i < str.length; ++i) {
	    // Gotcha: charCodeAt returns a 16-bit word that is a UTF-16 encoded code unit, not a Unicode code point of the character! So decode UTF16->UTF32->UTF8.
	    // See http://unicode.org/faq/utf_bom.html#utf16-3
	    // For UTF8 byte structure, see http://en.wikipedia.org/wiki/UTF-8#Description and https://www.ietf.org/rfc/rfc2279.txt and https://tools.ietf.org/html/rfc3629
	    var u = str.charCodeAt(i); // possibly a lead surrogate
	    if (u >= 0xD800 && u <= 0xDFFF) {
	      var u1 = str.charCodeAt(++i);
	      u = 0x10000 + ((u & 0x3FF) << 10) | (u1 & 0x3FF);
	    }
	    if (u <= 0x7F) {
	      if (outIdx >= endIdx) break;
	      outU8Array[outIdx++] = u;
	    } else if (u <= 0x7FF) {
	      if (outIdx + 1 >= endIdx) break;
	      outU8Array[outIdx++] = 0xC0 | (u >> 6);
	      outU8Array[outIdx++] = 0x80 | (u & 63);
	    } else if (u <= 0xFFFF) {
	      if (outIdx + 2 >= endIdx) break;
	      outU8Array[outIdx++] = 0xE0 | (u >> 12);
	      outU8Array[outIdx++] = 0x80 | ((u >> 6) & 63);
	      outU8Array[outIdx++] = 0x80 | (u & 63);
	    } else {
	      if (outIdx + 3 >= endIdx) break;
	      outU8Array[outIdx++] = 0xF0 | (u >> 18);
	      outU8Array[outIdx++] = 0x80 | ((u >> 12) & 63);
	      outU8Array[outIdx++] = 0x80 | ((u >> 6) & 63);
	      outU8Array[outIdx++] = 0x80 | (u & 63);
	    }
	  }
	  // Null-terminate the pointer to the buffer.
	  outU8Array[outIdx] = 0;
	  return outIdx - startIdx;
	}

	// Copies the given Javascript String object 'str' to the emscripten HEAP at address 'outPtr',
	// null-terminated and encoded in UTF8 form. The copy will require at most str.length*4+1 bytes of space in the HEAP.
	// Use the function lengthBytesUTF8 to compute the exact number of bytes (excluding null terminator) that this function will write.
	// Returns the number of bytes written, EXCLUDING the null terminator.

	function stringToUTF8(str, outPtr, maxBytesToWrite) {
	  return stringToUTF8Array(str, HEAPU8,outPtr, maxBytesToWrite);
	}

	// Returns the number of bytes the given Javascript string takes if encoded as a UTF8 byte array, EXCLUDING the null terminator byte.
	function lengthBytesUTF8(str) {
	  var len = 0;
	  for (var i = 0; i < str.length; ++i) {
	    // Gotcha: charCodeAt returns a 16-bit word that is a UTF-16 encoded code unit, not a Unicode code point of the character! So decode UTF16->UTF32->UTF8.
	    // See http://unicode.org/faq/utf_bom.html#utf16-3
	    var u = str.charCodeAt(i); // possibly a lead surrogate
	    if (u >= 0xD800 && u <= 0xDFFF) u = 0x10000 + ((u & 0x3FF) << 10) | (str.charCodeAt(++i) & 0x3FF);
	    if (u <= 0x7F) ++len;
	    else if (u <= 0x7FF) len += 2;
	    else if (u <= 0xFFFF) len += 3;
	    else len += 4;
	  }
	  return len;
	}

	// Given a pointer 'ptr' to a null-terminated UTF16LE-encoded string in the emscripten HEAP, returns
	// a copy of that string as a Javascript String object.

	typeof TextDecoder !== 'undefined' ? new TextDecoder('utf-16le') : undefined;

	// Allocate stack space for a JS string, and write it there.
	function allocateUTF8OnStack(str) {
	  var size = lengthBytesUTF8(str) + 1;
	  var ret = stackAlloc(size);
	  stringToUTF8Array(str, HEAP8, ret, size);
	  return ret;
	}

	function writeArrayToMemory(array, buffer) {
	  HEAP8.set(array, buffer);
	}

	function writeAsciiToMemory(str, buffer, dontAddNull) {
	  for (var i = 0; i < str.length; ++i) {
	    HEAP8[((buffer++)>>0)]=str.charCodeAt(i);
	  }
	  // Null-terminate the pointer to the HEAP.
	  if (!dontAddNull) HEAP8[((buffer)>>0)]=0;
	}
	var WASM_PAGE_SIZE = 65536;

	var /** @type {ArrayBuffer} */
	  buffer,
	/** @type {Int8Array} */
	  HEAP8,
	/** @type {Uint8Array} */
	  HEAPU8,
	/** @type {Int16Array} */
	  HEAP16,
	/** @type {Int32Array} */
	  HEAP32,
	/** @type {Float32Array} */
	  HEAPF32,
	/** @type {Float64Array} */
	  HEAPF64;

	function updateGlobalBufferAndViews(buf) {
	  buffer = buf;
	  Module['HEAP8'] = HEAP8 = new Int8Array(buf);
	  Module['HEAP16'] = HEAP16 = new Int16Array(buf);
	  Module['HEAP32'] = HEAP32 = new Int32Array(buf);
	  Module['HEAPU8'] = HEAPU8 = new Uint8Array(buf);
	  Module['HEAPU16'] = new Uint16Array(buf);
	  Module['HEAPU32'] = new Uint32Array(buf);
	  Module['HEAPF32'] = HEAPF32 = new Float32Array(buf);
	  Module['HEAPF64'] = HEAPF64 = new Float64Array(buf);
	}

	var DYNAMIC_BASE = 5267504,
	    DYNAMICTOP_PTR = 24464;

	var INITIAL_TOTAL_MEMORY = Module['TOTAL_MEMORY'] || 16777216;







	// In standalone mode, the wasm creates the memory, and the user can't provide it.
	// In non-standalone/normal mode, we create the memory here.

	// Create the main memory. (Note: this isn't used in STANDALONE_WASM mode since the wasm
	// memory is created in the wasm, not in JS.)

	  if (Module['wasmMemory']) {
	    wasmMemory = Module['wasmMemory'];
	  } else
	  {
	    wasmMemory = new WebAssembly.Memory({
	      'initial': INITIAL_TOTAL_MEMORY / WASM_PAGE_SIZE
	      ,
	      'maximum': INITIAL_TOTAL_MEMORY / WASM_PAGE_SIZE
	    });
	  }


	if (wasmMemory) {
	  buffer = wasmMemory.buffer;
	}

	// If the user provides an incorrect length, just use that length instead rather than providing the user to
	// specifically provide the memory length with Module['TOTAL_MEMORY'].
	INITIAL_TOTAL_MEMORY = buffer.byteLength;
	updateGlobalBufferAndViews(buffer);

	HEAP32[DYNAMICTOP_PTR>>2] = DYNAMIC_BASE;










	function callRuntimeCallbacks(callbacks) {
	  while(callbacks.length > 0) {
	    var callback = callbacks.shift();
	    if (typeof callback == 'function') {
	      callback();
	      continue;
	    }
	    var func = callback.func;
	    if (typeof func === 'number') {
	      if (callback.arg === undefined) {
	        Module['dynCall_v'](func);
	      } else {
	        Module['dynCall_vi'](func, callback.arg);
	      }
	    } else {
	      func(callback.arg === undefined ? null : callback.arg);
	    }
	  }
	}

	var __ATPRERUN__  = []; // functions called before the runtime is initialized
	var __ATINIT__    = []; // functions called during startup
	var __ATMAIN__    = []; // functions called when main() is to be run
	var __ATPOSTRUN__ = []; // functions called after the main() is called


	function preRun() {

	  if (Module['preRun']) {
	    if (typeof Module['preRun'] == 'function') Module['preRun'] = [Module['preRun']];
	    while (Module['preRun'].length) {
	      addOnPreRun(Module['preRun'].shift());
	    }
	  }

	  callRuntimeCallbacks(__ATPRERUN__);
	}

	function initRuntime() {
	  if (!Module["noFSInit"] && !FS.init.initialized) FS.init();
	  callRuntimeCallbacks(__ATINIT__);
	}

	function preMain() {
	  FS.ignorePermissions = false;
	  callRuntimeCallbacks(__ATMAIN__);
	}

	function postRun() {

	  if (Module['postRun']) {
	    if (typeof Module['postRun'] == 'function') Module['postRun'] = [Module['postRun']];
	    while (Module['postRun'].length) {
	      addOnPostRun(Module['postRun'].shift());
	    }
	  }

	  callRuntimeCallbacks(__ATPOSTRUN__);
	}

	function addOnPreRun(cb) {
	  __ATPRERUN__.unshift(cb);
	}

	function addOnPostRun(cb) {
	  __ATPOSTRUN__.unshift(cb);
	}


	// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/imul

	// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/fround

	// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/clz32

	// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/trunc


	var Math_abs = Math.abs;
	var Math_ceil = Math.ceil;
	var Math_floor = Math.floor;
	var Math_min = Math.min;



	// A counter of dependencies for calling run(). If we need to
	// do asynchronous work before running, increment this and
	// decrement it. Incrementing must happen in a place like
	// Module.preRun (used by emcc to add file preloading).
	// Note that you can add dependencies in preRun, even though
	// it happens right before run - run will be postponed until
	// the dependencies are met.
	var runDependencies = 0;
	var dependenciesFulfilled = null; // overridden to take different actions when all run dependencies are fulfilled

	function addRunDependency(id) {
	  runDependencies++;

	  if (Module['monitorRunDependencies']) {
	    Module['monitorRunDependencies'](runDependencies);
	  }

	}

	function removeRunDependency(id) {
	  runDependencies--;

	  if (Module['monitorRunDependencies']) {
	    Module['monitorRunDependencies'](runDependencies);
	  }

	  if (runDependencies == 0) {
	    if (dependenciesFulfilled) {
	      var callback = dependenciesFulfilled;
	      dependenciesFulfilled = null;
	      callback(); // can add another dependenciesFulfilled
	    }
	  }
	}

	Module["preloadedImages"] = {}; // maps url to image data
	Module["preloadedAudios"] = {}; // maps url to audio data


	function abort(what) {
	  if (Module['onAbort']) {
	    Module['onAbort'](what);
	  }

	  what += '';
	  out(what);
	  err(what);

	  ABORT = true;

	  what = 'abort(' + what + '). Build with -s ASSERTIONS=1 for more info.';

	  // Throw a wasm runtime error, because a JS error might be seen as a foreign
	  // exception, which means we'd run destructors on it. We need the error to
	  // simply make the program stop.
	  throw new WebAssembly.RuntimeError(what);
	}






	// Copyright 2017 The Emscripten Authors.  All rights reserved.
	// Emscripten is available under two separate licenses, the MIT license and the
	// University of Illinois/NCSA Open Source License.  Both these licenses can be
	// found in the LICENSE file.

	// Prefix of data URIs emitted by SINGLE_FILE and related options.
	var dataURIPrefix = 'data:application/octet-stream;base64,';

	// Indicates whether filename is a base64 data URI.
	function isDataURI(filename) {
	  return String.prototype.startsWith ?
	      filename.startsWith(dataURIPrefix) :
	      filename.indexOf(dataURIPrefix) === 0;
	}




	var wasmBinaryFile = 'data:application/octet-stream;base64,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';
	if (!isDataURI(wasmBinaryFile)) {
	  wasmBinaryFile = locateFile(wasmBinaryFile);
	}

	function getBinary() {
	  try {
	    if (wasmBinary) {
	      return new Uint8Array(wasmBinary);
	    }

	    var binary = tryParseAsDataURI(wasmBinaryFile);
	    if (binary) {
	      return binary;
	    }
	    if (readBinary) {
	      return readBinary(wasmBinaryFile);
	    } else {
	      throw "both async and sync fetching of the wasm failed";
	    }
	  }
	  catch (err) {
	    abort(err);
	  }
	}

	function getBinaryPromise() {
	  // if we don't have the binary yet, and have the Fetch api, use that
	  // in some environments, like Electron's render process, Fetch api may be present, but have a different context than expected, let's only use it on the Web
	  if (!wasmBinary && (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) && typeof fetch === 'function') {
	    return fetch(wasmBinaryFile, { credentials: 'same-origin' }).then(function(response) {
	      if (!response['ok']) {
	        throw "failed to load wasm binary file at '" + wasmBinaryFile + "'";
	      }
	      return response['arrayBuffer']();
	    }).catch(function () {
	      return getBinary();
	    });
	  }
	  // Otherwise, getBinary should be able to get it synchronously
	  return new Promise(function(resolve, reject) {
	    resolve(getBinary());
	  });
	}



	// Create the wasm instance.
	// Receives the wasm imports, returns the exports.
	function createWasm() {
	  // prepare imports
	  var info = {
	    'env': asmLibraryArg,
	    'wasi_snapshot_preview1': asmLibraryArg
	  };
	  // Load the wasm module and create an instance of using native support in the JS engine.
	  // handle a generated wasm instance, receiving its exports and
	  // performing other necessary setup
	  function receiveInstance(instance, module) {
	    var exports = instance.exports;
	    Module['asm'] = exports;
	    removeRunDependency();
	  }
	   // we can't run yet (except in a pthread, where we have a custom sync instantiator)
	  addRunDependency();


	  function receiveInstantiatedSource(output) {
	    // 'output' is a WebAssemblyInstantiatedSource object which has both the module and instance.
	    // receiveInstance() will swap in the exports (to Module.asm) so they can be called
	      // TODO: Due to Closure regression https://github.com/google/closure-compiler/issues/3193, the above line no longer optimizes out down to the following line.
	      // When the regression is fixed, can restore the above USE_PTHREADS-enabled path.
	    receiveInstance(output['instance']);
	  }


	  function instantiateArrayBuffer(receiver) {
	    return getBinaryPromise().then(function(binary) {
	      return WebAssembly.instantiate(binary, info);
	    }).then(receiver, function(reason) {
	      err('failed to asynchronously prepare wasm: ' + reason);
	      abort(reason);
	    });
	  }

	  // Prefer streaming instantiation if available.
	  function instantiateAsync() {
	    if (!wasmBinary &&
	        typeof WebAssembly.instantiateStreaming === 'function' &&
	        !isDataURI(wasmBinaryFile) &&
	        typeof fetch === 'function') {
	      fetch(wasmBinaryFile, { credentials: 'same-origin' }).then(function (response) {
	        var result = WebAssembly.instantiateStreaming(response, info);
	        return result.then(receiveInstantiatedSource, function(reason) {
	            // We expect the most common failure cause to be a bad MIME type for the binary,
	            // in which case falling back to ArrayBuffer instantiation should work.
	            err('wasm streaming compile failed: ' + reason);
	            err('falling back to ArrayBuffer instantiation');
	            instantiateArrayBuffer(receiveInstantiatedSource);
	          });
	      });
	    } else {
	      return instantiateArrayBuffer(receiveInstantiatedSource);
	    }
	  }
	  // User shell pages can write their own Module.instantiateWasm = function(imports, successCallback) callback
	  // to manually instantiate the Wasm module themselves. This allows pages to run the instantiation parallel
	  // to any other async startup actions they are performing.
	  if (Module['instantiateWasm']) {
	    try {
	      var exports = Module['instantiateWasm'](info, receiveInstance);
	      return exports;
	    } catch(e) {
	      err('Module.instantiateWasm callback failed with error: ' + e);
	      return false;
	    }
	  }

	  instantiateAsync();
	  return {}; // no exports yet; we'll fill them in later
	}


	// Globals used by JS i64 conversions
	var tempDouble;
	var tempI64;




	// STATICTOP = STATIC_BASE + 23600;
	/* global initializers */  __ATINIT__.push({ func: function() { ___wasm_call_ctors(); } });




	/* no memory initializer */
	// {{PRE_LIBRARY}}


	  function demangle(func) {
	      return func;
	    }

	  function demangleAll(text) {
	      var regex =
	        /\b_Z[\w\d_]+/g;
	      return text.replace(regex,
	        function(x) {
	          var y = demangle(x);
	          return x === y ? x : (y + ' [' + x + ']');
	        });
	    }

	  function jsStackTrace() {
	      var err = new Error();
	      if (!err.stack) {
	        // IE10+ special cases: It does have callstack info, but it is only populated if an Error object is thrown,
	        // so try that as a special-case.
	        try {
	          throw new Error();
	        } catch(e) {
	          err = e;
	        }
	        if (!err.stack) {
	          return '(no stack trace available)';
	        }
	      }
	      return err.stack.toString();
	    }

	  function stackTrace() {
	      var js = jsStackTrace();
	      if (Module['extraStackTrace']) js += '\n' + Module['extraStackTrace']();
	      return demangleAll(js);
	    }

	  function ___assert_fail(condition, filename, line, func) {
	      abort('Assertion failed: ' + UTF8ToString(condition) + ', at: ' + [filename ? UTF8ToString(filename) : 'unknown filename', line, func ? UTF8ToString(func) : 'unknown function']);
	    }

	  
	  
	  function _emscripten_get_now() { abort(); }
	  
	  var _emscripten_get_now_is_monotonic=
	       (ENVIRONMENT_IS_NODE
	        || (typeof dateNow !== 'undefined')
	  
	        // Modern environment where performance.now() is supported: (rely on minifier to return true unconditionally from this function)
	        || 1
	  
	        );  
	  function ___setErrNo(value) {
	      if (Module['___errno_location']) HEAP32[((Module['___errno_location']())>>2)]=value;
	      return value;
	    }function _clock_gettime(clk_id, tp) {
	      // int clock_gettime(clockid_t clk_id, struct timespec *tp);
	      var now;
	      if (clk_id === 0) {
	        now = Date.now();
	      } else if (clk_id === 1 && _emscripten_get_now_is_monotonic) {
	        now = _emscripten_get_now();
	      } else {
	        ___setErrNo(28);
	        return -1;
	      }
	      HEAP32[((tp)>>2)]=(now/1000)|0; // seconds
	      HEAP32[(((tp)+(4))>>2)]=((now % 1000)*1000*1000)|0; // nanoseconds
	      return 0;
	    }function ___clock_gettime(a0,a1
	  ) {
	  return _clock_gettime(a0,a1);
	  }

	  function ___lock() {}

	  
	  
	  var PATH={splitPath:function(filename) {
	        var splitPathRe = /^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;
	        return splitPathRe.exec(filename).slice(1);
	      },normalizeArray:function(parts, allowAboveRoot) {
	        // if the path tries to go above the root, `up` ends up > 0
	        var up = 0;
	        for (var i = parts.length - 1; i >= 0; i--) {
	          var last = parts[i];
	          if (last === '.') {
	            parts.splice(i, 1);
	          } else if (last === '..') {
	            parts.splice(i, 1);
	            up++;
	          } else if (up) {
	            parts.splice(i, 1);
	            up--;
	          }
	        }
	        // if the path is allowed to go above the root, restore leading ..s
	        if (allowAboveRoot) {
	          for (; up; up--) {
	            parts.unshift('..');
	          }
	        }
	        return parts;
	      },normalize:function(path) {
	        var isAbsolute = path.charAt(0) === '/',
	            trailingSlash = path.substr(-1) === '/';
	        // Normalize the path
	        path = PATH.normalizeArray(path.split('/').filter(function(p) {
	          return !!p;
	        }), !isAbsolute).join('/');
	        if (!path && !isAbsolute) {
	          path = '.';
	        }
	        if (path && trailingSlash) {
	          path += '/';
	        }
	        return (isAbsolute ? '/' : '') + path;
	      },dirname:function(path) {
	        var result = PATH.splitPath(path),
	            root = result[0],
	            dir = result[1];
	        if (!root && !dir) {
	          // No dirname whatsoever
	          return '.';
	        }
	        if (dir) {
	          // It has a dirname, strip trailing slash
	          dir = dir.substr(0, dir.length - 1);
	        }
	        return root + dir;
	      },basename:function(path) {
	        // EMSCRIPTEN return '/'' for '/', not an empty string
	        if (path === '/') return '/';
	        var lastSlash = path.lastIndexOf('/');
	        if (lastSlash === -1) return path;
	        return path.substr(lastSlash+1);
	      },extname:function(path) {
	        return PATH.splitPath(path)[3];
	      },join:function() {
	        var paths = Array.prototype.slice.call(arguments, 0);
	        return PATH.normalize(paths.join('/'));
	      },join2:function(l, r) {
	        return PATH.normalize(l + '/' + r);
	      }};
	  
	  
	  var PATH_FS={resolve:function() {
	        var resolvedPath = '',
	          resolvedAbsolute = false;
	        for (var i = arguments.length - 1; i >= -1 && !resolvedAbsolute; i--) {
	          var path = (i >= 0) ? arguments[i] : FS.cwd();
	          // Skip empty and invalid entries
	          if (typeof path !== 'string') {
	            throw new TypeError('Arguments to path.resolve must be strings');
	          } else if (!path) {
	            return ''; // an invalid portion invalidates the whole thing
	          }
	          resolvedPath = path + '/' + resolvedPath;
	          resolvedAbsolute = path.charAt(0) === '/';
	        }
	        // At this point the path should be resolved to a full absolute path, but
	        // handle relative paths to be safe (might happen when process.cwd() fails)
	        resolvedPath = PATH.normalizeArray(resolvedPath.split('/').filter(function(p) {
	          return !!p;
	        }), !resolvedAbsolute).join('/');
	        return ((resolvedAbsolute ? '/' : '') + resolvedPath) || '.';
	      },relative:function(from, to) {
	        from = PATH_FS.resolve(from).substr(1);
	        to = PATH_FS.resolve(to).substr(1);
	        function trim(arr) {
	          var start = 0;
	          for (; start < arr.length; start++) {
	            if (arr[start] !== '') break;
	          }
	          var end = arr.length - 1;
	          for (; end >= 0; end--) {
	            if (arr[end] !== '') break;
	          }
	          if (start > end) return [];
	          return arr.slice(start, end - start + 1);
	        }
	        var fromParts = trim(from.split('/'));
	        var toParts = trim(to.split('/'));
	        var length = Math.min(fromParts.length, toParts.length);
	        var samePartsLength = length;
	        for (var i = 0; i < length; i++) {
	          if (fromParts[i] !== toParts[i]) {
	            samePartsLength = i;
	            break;
	          }
	        }
	        var outputParts = [];
	        for (var i = samePartsLength; i < fromParts.length; i++) {
	          outputParts.push('..');
	        }
	        outputParts = outputParts.concat(toParts.slice(samePartsLength));
	        return outputParts.join('/');
	      }};
	  
	  var TTY={ttys:[],init:function () {
	        // https://github.com/emscripten-core/emscripten/pull/1555
	        // if (ENVIRONMENT_IS_NODE) {
	        //   // currently, FS.init does not distinguish if process.stdin is a file or TTY
	        //   // device, it always assumes it's a TTY device. because of this, we're forcing
	        //   // process.stdin to UTF8 encoding to at least make stdin reading compatible
	        //   // with text files until FS.init can be refactored.
	        //   process['stdin']['setEncoding']('utf8');
	        // }
	      },shutdown:function() {
	        // https://github.com/emscripten-core/emscripten/pull/1555
	        // if (ENVIRONMENT_IS_NODE) {
	        //   // inolen: any idea as to why node -e 'process.stdin.read()' wouldn't exit immediately (with process.stdin being a tty)?
	        //   // isaacs: because now it's reading from the stream, you've expressed interest in it, so that read() kicks off a _read() which creates a ReadReq operation
	        //   // inolen: I thought read() in that case was a synchronous operation that just grabbed some amount of buffered data if it exists?
	        //   // isaacs: it is. but it also triggers a _read() call, which calls readStart() on the handle
	        //   // isaacs: do process.stdin.pause() and i'd think it'd probably close the pending call
	        //   process['stdin']['pause']();
	        // }
	      },register:function(dev, ops) {
	        TTY.ttys[dev] = { input: [], output: [], ops: ops };
	        FS.registerDevice(dev, TTY.stream_ops);
	      },stream_ops:{open:function(stream) {
	          var tty = TTY.ttys[stream.node.rdev];
	          if (!tty) {
	            throw new FS.ErrnoError(43);
	          }
	          stream.tty = tty;
	          stream.seekable = false;
	        },close:function(stream) {
	          // flush any pending line data
	          stream.tty.ops.flush(stream.tty);
	        },flush:function(stream) {
	          stream.tty.ops.flush(stream.tty);
	        },read:function(stream, buffer, offset, length, pos /* ignored */) {
	          if (!stream.tty || !stream.tty.ops.get_char) {
	            throw new FS.ErrnoError(60);
	          }
	          var bytesRead = 0;
	          for (var i = 0; i < length; i++) {
	            var result;
	            try {
	              result = stream.tty.ops.get_char(stream.tty);
	            } catch (e) {
	              throw new FS.ErrnoError(29);
	            }
	            if (result === undefined && bytesRead === 0) {
	              throw new FS.ErrnoError(6);
	            }
	            if (result === null || result === undefined) break;
	            bytesRead++;
	            buffer[offset+i] = result;
	          }
	          if (bytesRead) {
	            stream.node.timestamp = Date.now();
	          }
	          return bytesRead;
	        },write:function(stream, buffer, offset, length, pos) {
	          if (!stream.tty || !stream.tty.ops.put_char) {
	            throw new FS.ErrnoError(60);
	          }
	          try {
	            for (var i = 0; i < length; i++) {
	              stream.tty.ops.put_char(stream.tty, buffer[offset+i]);
	            }
	          } catch (e) {
	            throw new FS.ErrnoError(29);
	          }
	          if (length) {
	            stream.node.timestamp = Date.now();
	          }
	          return i;
	        }},default_tty_ops:{get_char:function(tty) {
	          if (!tty.input.length) {
	            var result = null;
	            if (ENVIRONMENT_IS_NODE) {
	              // we will read data by chunks of BUFSIZE
	              var BUFSIZE = 256;
	              var buf = Buffer.alloc ? Buffer.alloc(BUFSIZE) : new Buffer(BUFSIZE);
	              var bytesRead = 0;
	  
	              try {
	                bytesRead = nodeFS.readSync(process.stdin.fd, buf, 0, BUFSIZE, null);
	              } catch(e) {
	                // Cross-platform differences: on Windows, reading EOF throws an exception, but on other OSes,
	                // reading EOF returns 0. Uniformize behavior by treating the EOF exception to return 0.
	                if (e.toString().indexOf('EOF') != -1) bytesRead = 0;
	                else throw e;
	              }
	  
	              if (bytesRead > 0) {
	                result = buf.slice(0, bytesRead).toString('utf-8');
	              } else {
	                result = null;
	              }
	            } else
	            if (typeof window != 'undefined' &&
	              typeof window.prompt == 'function') {
	              // Browser.
	              result = window.prompt('Input: ');  // returns null on cancel
	              if (result !== null) {
	                result += '\n';
	              }
	            } else if (typeof readline == 'function') {
	              // Command line.
	              result = readline();
	              if (result !== null) {
	                result += '\n';
	              }
	            }
	            if (!result) {
	              return null;
	            }
	            tty.input = intArrayFromString(result, true);
	          }
	          return tty.input.shift();
	        },put_char:function(tty, val) {
	          if (val === null || val === 10) {
	            out(UTF8ArrayToString(tty.output, 0));
	            tty.output = [];
	          } else {
	            if (val != 0) tty.output.push(val); // val == 0 would cut text output off in the middle.
	          }
	        },flush:function(tty) {
	          if (tty.output && tty.output.length > 0) {
	            out(UTF8ArrayToString(tty.output, 0));
	            tty.output = [];
	          }
	        }},default_tty1_ops:{put_char:function(tty, val) {
	          if (val === null || val === 10) {
	            err(UTF8ArrayToString(tty.output, 0));
	            tty.output = [];
	          } else {
	            if (val != 0) tty.output.push(val);
	          }
	        },flush:function(tty) {
	          if (tty.output && tty.output.length > 0) {
	            err(UTF8ArrayToString(tty.output, 0));
	            tty.output = [];
	          }
	        }}};
	  
	  var MEMFS={ops_table:null,mount:function(mount) {
	        return MEMFS.createNode(null, '/', 16384 | 511 /* 0777 */, 0);
	      },createNode:function(parent, name, mode, dev) {
	        if (FS.isBlkdev(mode) || FS.isFIFO(mode)) {
	          // no supported
	          throw new FS.ErrnoError(63);
	        }
	        if (!MEMFS.ops_table) {
	          MEMFS.ops_table = {
	            dir: {
	              node: {
	                getattr: MEMFS.node_ops.getattr,
	                setattr: MEMFS.node_ops.setattr,
	                lookup: MEMFS.node_ops.lookup,
	                mknod: MEMFS.node_ops.mknod,
	                rename: MEMFS.node_ops.rename,
	                unlink: MEMFS.node_ops.unlink,
	                rmdir: MEMFS.node_ops.rmdir,
	                readdir: MEMFS.node_ops.readdir,
	                symlink: MEMFS.node_ops.symlink
	              },
	              stream: {
	                llseek: MEMFS.stream_ops.llseek
	              }
	            },
	            file: {
	              node: {
	                getattr: MEMFS.node_ops.getattr,
	                setattr: MEMFS.node_ops.setattr
	              },
	              stream: {
	                llseek: MEMFS.stream_ops.llseek,
	                read: MEMFS.stream_ops.read,
	                write: MEMFS.stream_ops.write,
	                allocate: MEMFS.stream_ops.allocate,
	                mmap: MEMFS.stream_ops.mmap,
	                msync: MEMFS.stream_ops.msync
	              }
	            },
	            link: {
	              node: {
	                getattr: MEMFS.node_ops.getattr,
	                setattr: MEMFS.node_ops.setattr,
	                readlink: MEMFS.node_ops.readlink
	              },
	              stream: {}
	            },
	            chrdev: {
	              node: {
	                getattr: MEMFS.node_ops.getattr,
	                setattr: MEMFS.node_ops.setattr
	              },
	              stream: FS.chrdev_stream_ops
	            }
	          };
	        }
	        var node = FS.createNode(parent, name, mode, dev);
	        if (FS.isDir(node.mode)) {
	          node.node_ops = MEMFS.ops_table.dir.node;
	          node.stream_ops = MEMFS.ops_table.dir.stream;
	          node.contents = {};
	        } else if (FS.isFile(node.mode)) {
	          node.node_ops = MEMFS.ops_table.file.node;
	          node.stream_ops = MEMFS.ops_table.file.stream;
	          node.usedBytes = 0; // The actual number of bytes used in the typed array, as opposed to contents.length which gives the whole capacity.
	          // When the byte data of the file is populated, this will point to either a typed array, or a normal JS array. Typed arrays are preferred
	          // for performance, and used by default. However, typed arrays are not resizable like normal JS arrays are, so there is a small disk size
	          // penalty involved for appending file writes that continuously grow a file similar to std::vector capacity vs used -scheme.
	          node.contents = null; 
	        } else if (FS.isLink(node.mode)) {
	          node.node_ops = MEMFS.ops_table.link.node;
	          node.stream_ops = MEMFS.ops_table.link.stream;
	        } else if (FS.isChrdev(node.mode)) {
	          node.node_ops = MEMFS.ops_table.chrdev.node;
	          node.stream_ops = MEMFS.ops_table.chrdev.stream;
	        }
	        node.timestamp = Date.now();
	        // add the new node to the parent
	        if (parent) {
	          parent.contents[name] = node;
	        }
	        return node;
	      },getFileDataAsRegularArray:function(node) {
	        if (node.contents && node.contents.subarray) {
	          var arr = [];
	          for (var i = 0; i < node.usedBytes; ++i) arr.push(node.contents[i]);
	          return arr; // Returns a copy of the original data.
	        }
	        return node.contents; // No-op, the file contents are already in a JS array. Return as-is.
	      },getFileDataAsTypedArray:function(node) {
	        if (!node.contents) return new Uint8Array;
	        if (node.contents.subarray) return node.contents.subarray(0, node.usedBytes); // Make sure to not return excess unused bytes.
	        return new Uint8Array(node.contents);
	      },expandFileStorage:function(node, newCapacity) {
	        var prevCapacity = node.contents ? node.contents.length : 0;
	        if (prevCapacity >= newCapacity) return; // No need to expand, the storage was already large enough.
	        // Don't expand strictly to the given requested limit if it's only a very small increase, but instead geometrically grow capacity.
	        // For small filesizes (<1MB), perform size*2 geometric increase, but for large sizes, do a much more conservative size*1.125 increase to
	        // avoid overshooting the allocation cap by a very large margin.
	        var CAPACITY_DOUBLING_MAX = 1024 * 1024;
	        newCapacity = Math.max(newCapacity, (prevCapacity * (prevCapacity < CAPACITY_DOUBLING_MAX ? 2.0 : 1.125)) | 0);
	        if (prevCapacity != 0) newCapacity = Math.max(newCapacity, 256); // At minimum allocate 256b for each file when expanding.
	        var oldContents = node.contents;
	        node.contents = new Uint8Array(newCapacity); // Allocate new storage.
	        if (node.usedBytes > 0) node.contents.set(oldContents.subarray(0, node.usedBytes), 0); // Copy old data over to the new storage.
	        return;
	      },resizeFileStorage:function(node, newSize) {
	        if (node.usedBytes == newSize) return;
	        if (newSize == 0) {
	          node.contents = null; // Fully decommit when requesting a resize to zero.
	          node.usedBytes = 0;
	          return;
	        }
	        if (!node.contents || node.contents.subarray) { // Resize a typed array if that is being used as the backing store.
	          var oldContents = node.contents;
	          node.contents = new Uint8Array(new ArrayBuffer(newSize)); // Allocate new storage.
	          if (oldContents) {
	            node.contents.set(oldContents.subarray(0, Math.min(newSize, node.usedBytes))); // Copy old data over to the new storage.
	          }
	          node.usedBytes = newSize;
	          return;
	        }
	        // Backing with a JS array.
	        if (!node.contents) node.contents = [];
	        if (node.contents.length > newSize) node.contents.length = newSize;
	        else while (node.contents.length < newSize) node.contents.push(0);
	        node.usedBytes = newSize;
	      },node_ops:{getattr:function(node) {
	          var attr = {};
	          // device numbers reuse inode numbers.
	          attr.dev = FS.isChrdev(node.mode) ? node.id : 1;
	          attr.ino = node.id;
	          attr.mode = node.mode;
	          attr.nlink = 1;
	          attr.uid = 0;
	          attr.gid = 0;
	          attr.rdev = node.rdev;
	          if (FS.isDir(node.mode)) {
	            attr.size = 4096;
	          } else if (FS.isFile(node.mode)) {
	            attr.size = node.usedBytes;
	          } else if (FS.isLink(node.mode)) {
	            attr.size = node.link.length;
	          } else {
	            attr.size = 0;
	          }
	          attr.atime = new Date(node.timestamp);
	          attr.mtime = new Date(node.timestamp);
	          attr.ctime = new Date(node.timestamp);
	          // NOTE: In our implementation, st_blocks = Math.ceil(st_size/st_blksize),
	          //       but this is not required by the standard.
	          attr.blksize = 4096;
	          attr.blocks = Math.ceil(attr.size / attr.blksize);
	          return attr;
	        },setattr:function(node, attr) {
	          if (attr.mode !== undefined) {
	            node.mode = attr.mode;
	          }
	          if (attr.timestamp !== undefined) {
	            node.timestamp = attr.timestamp;
	          }
	          if (attr.size !== undefined) {
	            MEMFS.resizeFileStorage(node, attr.size);
	          }
	        },lookup:function(parent, name) {
	          throw FS.genericErrors[44];
	        },mknod:function(parent, name, mode, dev) {
	          return MEMFS.createNode(parent, name, mode, dev);
	        },rename:function(old_node, new_dir, new_name) {
	          // if we're overwriting a directory at new_name, make sure it's empty.
	          if (FS.isDir(old_node.mode)) {
	            var new_node;
	            try {
	              new_node = FS.lookupNode(new_dir, new_name);
	            } catch (e) {
	            }
	            if (new_node) {
	              for (var i in new_node.contents) {
	                throw new FS.ErrnoError(55);
	              }
	            }
	          }
	          // do the internal rewiring
	          delete old_node.parent.contents[old_node.name];
	          old_node.name = new_name;
	          new_dir.contents[new_name] = old_node;
	          old_node.parent = new_dir;
	        },unlink:function(parent, name) {
	          delete parent.contents[name];
	        },rmdir:function(parent, name) {
	          var node = FS.lookupNode(parent, name);
	          for (var i in node.contents) {
	            throw new FS.ErrnoError(55);
	          }
	          delete parent.contents[name];
	        },readdir:function(node) {
	          var entries = ['.', '..'];
	          for (var key in node.contents) {
	            if (!node.contents.hasOwnProperty(key)) {
	              continue;
	            }
	            entries.push(key);
	          }
	          return entries;
	        },symlink:function(parent, newname, oldpath) {
	          var node = MEMFS.createNode(parent, newname, 511 /* 0777 */ | 40960, 0);
	          node.link = oldpath;
	          return node;
	        },readlink:function(node) {
	          if (!FS.isLink(node.mode)) {
	            throw new FS.ErrnoError(28);
	          }
	          return node.link;
	        }},stream_ops:{read:function(stream, buffer, offset, length, position) {
	          var contents = stream.node.contents;
	          if (position >= stream.node.usedBytes) return 0;
	          var size = Math.min(stream.node.usedBytes - position, length);
	          if (size > 8 && contents.subarray) { // non-trivial, and typed array
	            buffer.set(contents.subarray(position, position + size), offset);
	          } else {
	            for (var i = 0; i < size; i++) buffer[offset + i] = contents[position + i];
	          }
	          return size;
	        },write:function(stream, buffer, offset, length, position, canOwn) {
	  
	          if (!length) return 0;
	          var node = stream.node;
	          node.timestamp = Date.now();
	  
	          if (buffer.subarray && (!node.contents || node.contents.subarray)) { // This write is from a typed array to a typed array?
	            if (canOwn) {
	              node.contents = buffer.subarray(offset, offset + length);
	              node.usedBytes = length;
	              return length;
	            } else if (node.usedBytes === 0 && position === 0) { // If this is a simple first write to an empty file, do a fast set since we don't need to care about old data.
	              node.contents = new Uint8Array(buffer.subarray(offset, offset + length));
	              node.usedBytes = length;
	              return length;
	            } else if (position + length <= node.usedBytes) { // Writing to an already allocated and used subrange of the file?
	              node.contents.set(buffer.subarray(offset, offset + length), position);
	              return length;
	            }
	          }
	  
	          // Appending to an existing file and we need to reallocate, or source data did not come as a typed array.
	          MEMFS.expandFileStorage(node, position+length);
	          if (node.contents.subarray && buffer.subarray) node.contents.set(buffer.subarray(offset, offset + length), position); // Use typed array write if available.
	          else {
	            for (var i = 0; i < length; i++) {
	             node.contents[position + i] = buffer[offset + i]; // Or fall back to manual write if not.
	            }
	          }
	          node.usedBytes = Math.max(node.usedBytes, position+length);
	          return length;
	        },llseek:function(stream, offset, whence) {
	          var position = offset;
	          if (whence === 1) {
	            position += stream.position;
	          } else if (whence === 2) {
	            if (FS.isFile(stream.node.mode)) {
	              position += stream.node.usedBytes;
	            }
	          }
	          if (position < 0) {
	            throw new FS.ErrnoError(28);
	          }
	          return position;
	        },allocate:function(stream, offset, length) {
	          MEMFS.expandFileStorage(stream.node, offset + length);
	          stream.node.usedBytes = Math.max(stream.node.usedBytes, offset + length);
	        },mmap:function(stream, buffer, offset, length, position, prot, flags) {
	          if (!FS.isFile(stream.node.mode)) {
	            throw new FS.ErrnoError(43);
	          }
	          var ptr;
	          var allocated;
	          var contents = stream.node.contents;
	          // Only make a new copy when MAP_PRIVATE is specified.
	          if ( !(flags & 2) &&
	                contents.buffer === buffer.buffer ) {
	            // We can't emulate MAP_SHARED when the file is not backed by the buffer
	            // we're mapping to (e.g. the HEAP buffer).
	            allocated = false;
	            ptr = contents.byteOffset;
	          } else {
	            // Try to avoid unnecessary slices.
	            if (position > 0 || position + length < stream.node.usedBytes) {
	              if (contents.subarray) {
	                contents = contents.subarray(position, position + length);
	              } else {
	                contents = Array.prototype.slice.call(contents, position, position + length);
	              }
	            }
	            allocated = true;
	            // malloc() can lead to growing the heap. If targeting the heap, we need to
	            // re-acquire the heap buffer object in case growth had occurred.
	            var fromHeap = (buffer.buffer == HEAP8.buffer);
	            ptr = _malloc(length);
	            if (!ptr) {
	              throw new FS.ErrnoError(48);
	            }
	            (fromHeap ? HEAP8 : buffer).set(contents, ptr);
	          }
	          return { ptr: ptr, allocated: allocated };
	        },msync:function(stream, buffer, offset, length, mmapFlags) {
	          if (!FS.isFile(stream.node.mode)) {
	            throw new FS.ErrnoError(43);
	          }
	          if (mmapFlags & 2) {
	            // MAP_PRIVATE calls need not to be synced back to underlying fs
	            return 0;
	          }
	  
	          MEMFS.stream_ops.write(stream, buffer, 0, length, offset, false);
	          // should we check if bytesWritten and length are the same?
	          return 0;
	        }}};var FS={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:false,ignorePermissions:true,trackingDelegate:{},tracking:{openFlags:{READ:1,WRITE:2}},ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,handleFSError:function(e) {
	        if (!(e instanceof FS.ErrnoError)) throw e + ' : ' + stackTrace();
	        return ___setErrNo(e.errno);
	      },lookupPath:function(path, opts) {
	        path = PATH_FS.resolve(FS.cwd(), path);
	        opts = opts || {};
	  
	        if (!path) return { path: '', node: null };
	  
	        var defaults = {
	          follow_mount: true,
	          recurse_count: 0
	        };
	        for (var key in defaults) {
	          if (opts[key] === undefined) {
	            opts[key] = defaults[key];
	          }
	        }
	  
	        if (opts.recurse_count > 8) {  // max recursive lookup of 8
	          throw new FS.ErrnoError(32);
	        }
	  
	        // split the path
	        var parts = PATH.normalizeArray(path.split('/').filter(function(p) {
	          return !!p;
	        }), false);
	  
	        // start at the root
	        var current = FS.root;
	        var current_path = '/';
	  
	        for (var i = 0; i < parts.length; i++) {
	          var islast = (i === parts.length-1);
	          if (islast && opts.parent) {
	            // stop resolving
	            break;
	          }
	  
	          current = FS.lookupNode(current, parts[i]);
	          current_path = PATH.join2(current_path, parts[i]);
	  
	          // jump to the mount's root node if this is a mountpoint
	          if (FS.isMountpoint(current)) {
	            if (!islast || (islast && opts.follow_mount)) {
	              current = current.mounted.root;
	            }
	          }
	  
	          // by default, lookupPath will not follow a symlink if it is the final path component.
	          // setting opts.follow = true will override this behavior.
	          if (!islast || opts.follow) {
	            var count = 0;
	            while (FS.isLink(current.mode)) {
	              var link = FS.readlink(current_path);
	              current_path = PATH_FS.resolve(PATH.dirname(current_path), link);
	  
	              var lookup = FS.lookupPath(current_path, { recurse_count: opts.recurse_count });
	              current = lookup.node;
	  
	              if (count++ > 40) {  // limit max consecutive symlinks to 40 (SYMLOOP_MAX).
	                throw new FS.ErrnoError(32);
	              }
	            }
	          }
	        }
	  
	        return { path: current_path, node: current };
	      },getPath:function(node) {
	        var path;
	        while (true) {
	          if (FS.isRoot(node)) {
	            var mount = node.mount.mountpoint;
	            if (!path) return mount;
	            return mount[mount.length-1] !== '/' ? mount + '/' + path : mount + path;
	          }
	          path = path ? node.name + '/' + path : node.name;
	          node = node.parent;
	        }
	      },hashName:function(parentid, name) {
	        var hash = 0;
	  
	  
	        for (var i = 0; i < name.length; i++) {
	          hash = ((hash << 5) - hash + name.charCodeAt(i)) | 0;
	        }
	        return ((parentid + hash) >>> 0) % FS.nameTable.length;
	      },hashAddNode:function(node) {
	        var hash = FS.hashName(node.parent.id, node.name);
	        node.name_next = FS.nameTable[hash];
	        FS.nameTable[hash] = node;
	      },hashRemoveNode:function(node) {
	        var hash = FS.hashName(node.parent.id, node.name);
	        if (FS.nameTable[hash] === node) {
	          FS.nameTable[hash] = node.name_next;
	        } else {
	          var current = FS.nameTable[hash];
	          while (current) {
	            if (current.name_next === node) {
	              current.name_next = node.name_next;
	              break;
	            }
	            current = current.name_next;
	          }
	        }
	      },lookupNode:function(parent, name) {
	        var errCode = FS.mayLookup(parent);
	        if (errCode) {
	          throw new FS.ErrnoError(errCode, parent);
	        }
	        var hash = FS.hashName(parent.id, name);
	        for (var node = FS.nameTable[hash]; node; node = node.name_next) {
	          var nodeName = node.name;
	          if (node.parent.id === parent.id && nodeName === name) {
	            return node;
	          }
	        }
	        // if we failed to find it in the cache, call into the VFS
	        return FS.lookup(parent, name);
	      },createNode:function(parent, name, mode, rdev) {
	        if (!FS.FSNode) {
	          FS.FSNode = function(parent, name, mode, rdev) {
	            if (!parent) {
	              parent = this;  // root node sets parent to itself
	            }
	            this.parent = parent;
	            this.mount = parent.mount;
	            this.mounted = null;
	            this.id = FS.nextInode++;
	            this.name = name;
	            this.mode = mode;
	            this.node_ops = {};
	            this.stream_ops = {};
	            this.rdev = rdev;
	          };
	  
	          FS.FSNode.prototype = {};
	  
	          // compatibility
	          var readMode = 292 | 73;
	          var writeMode = 146;
	  
	          // NOTE we must use Object.defineProperties instead of individual calls to
	          // Object.defineProperty in order to make closure compiler happy
	          Object.defineProperties(FS.FSNode.prototype, {
	            read: {
	              get: function() { return (this.mode & readMode) === readMode; },
	              set: function(val) { val ? this.mode |= readMode : this.mode &= ~readMode; }
	            },
	            write: {
	              get: function() { return (this.mode & writeMode) === writeMode; },
	              set: function(val) { val ? this.mode |= writeMode : this.mode &= ~writeMode; }
	            },
	            isFolder: {
	              get: function() { return FS.isDir(this.mode); }
	            },
	            isDevice: {
	              get: function() { return FS.isChrdev(this.mode); }
	            }
	          });
	        }
	  
	        var node = new FS.FSNode(parent, name, mode, rdev);
	  
	        FS.hashAddNode(node);
	  
	        return node;
	      },destroyNode:function(node) {
	        FS.hashRemoveNode(node);
	      },isRoot:function(node) {
	        return node === node.parent;
	      },isMountpoint:function(node) {
	        return !!node.mounted;
	      },isFile:function(mode) {
	        return (mode & 61440) === 32768;
	      },isDir:function(mode) {
	        return (mode & 61440) === 16384;
	      },isLink:function(mode) {
	        return (mode & 61440) === 40960;
	      },isChrdev:function(mode) {
	        return (mode & 61440) === 8192;
	      },isBlkdev:function(mode) {
	        return (mode & 61440) === 24576;
	      },isFIFO:function(mode) {
	        return (mode & 61440) === 4096;
	      },isSocket:function(mode) {
	        return (mode & 49152) === 49152;
	      },flagModes:{"r":0,"rs":1052672,"r+":2,"w":577,"wx":705,"xw":705,"w+":578,"wx+":706,"xw+":706,"a":1089,"ax":1217,"xa":1217,"a+":1090,"ax+":1218,"xa+":1218},modeStringToFlags:function(str) {
	        var flags = FS.flagModes[str];
	        if (typeof flags === 'undefined') {
	          throw new Error('Unknown file open mode: ' + str);
	        }
	        return flags;
	      },flagsToPermissionString:function(flag) {
	        var perms = ['r', 'w', 'rw'][flag & 3];
	        if ((flag & 512)) {
	          perms += 'w';
	        }
	        return perms;
	      },nodePermissions:function(node, perms) {
	        if (FS.ignorePermissions) {
	          return 0;
	        }
	        // return 0 if any user, group or owner bits are set.
	        if (perms.indexOf('r') !== -1 && !(node.mode & 292)) {
	          return 2;
	        } else if (perms.indexOf('w') !== -1 && !(node.mode & 146)) {
	          return 2;
	        } else if (perms.indexOf('x') !== -1 && !(node.mode & 73)) {
	          return 2;
	        }
	        return 0;
	      },mayLookup:function(dir) {
	        var errCode = FS.nodePermissions(dir, 'x');
	        if (errCode) return errCode;
	        if (!dir.node_ops.lookup) return 2;
	        return 0;
	      },mayCreate:function(dir, name) {
	        try {
	          var node = FS.lookupNode(dir, name);
	          return 20;
	        } catch (e) {
	        }
	        return FS.nodePermissions(dir, 'wx');
	      },mayDelete:function(dir, name, isdir) {
	        var node;
	        try {
	          node = FS.lookupNode(dir, name);
	        } catch (e) {
	          return e.errno;
	        }
	        var errCode = FS.nodePermissions(dir, 'wx');
	        if (errCode) {
	          return errCode;
	        }
	        if (isdir) {
	          if (!FS.isDir(node.mode)) {
	            return 54;
	          }
	          if (FS.isRoot(node) || FS.getPath(node) === FS.cwd()) {
	            return 10;
	          }
	        } else {
	          if (FS.isDir(node.mode)) {
	            return 31;
	          }
	        }
	        return 0;
	      },mayOpen:function(node, flags) {
	        if (!node) {
	          return 44;
	        }
	        if (FS.isLink(node.mode)) {
	          return 32;
	        } else if (FS.isDir(node.mode)) {
	          if (FS.flagsToPermissionString(flags) !== 'r' || // opening for write
	              (flags & 512)) { // TODO: check for O_SEARCH? (== search for dir only)
	            return 31;
	          }
	        }
	        return FS.nodePermissions(node, FS.flagsToPermissionString(flags));
	      },MAX_OPEN_FDS:4096,nextfd:function(fd_start, fd_end) {
	        fd_start = fd_start || 0;
	        fd_end = fd_end || FS.MAX_OPEN_FDS;
	        for (var fd = fd_start; fd <= fd_end; fd++) {
	          if (!FS.streams[fd]) {
	            return fd;
	          }
	        }
	        throw new FS.ErrnoError(33);
	      },getStream:function(fd) {
	        return FS.streams[fd];
	      },createStream:function(stream, fd_start, fd_end) {
	        if (!FS.FSStream) {
	          FS.FSStream = function(){};
	          FS.FSStream.prototype = {};
	          // compatibility
	          Object.defineProperties(FS.FSStream.prototype, {
	            object: {
	              get: function() { return this.node; },
	              set: function(val) { this.node = val; }
	            },
	            isRead: {
	              get: function() { return (this.flags & 2097155) !== 1; }
	            },
	            isWrite: {
	              get: function() { return (this.flags & 2097155) !== 0; }
	            },
	            isAppend: {
	              get: function() { return (this.flags & 1024); }
	            }
	          });
	        }
	        // clone it, so we can return an instance of FSStream
	        var newStream = new FS.FSStream();
	        for (var p in stream) {
	          newStream[p] = stream[p];
	        }
	        stream = newStream;
	        var fd = FS.nextfd(fd_start, fd_end);
	        stream.fd = fd;
	        FS.streams[fd] = stream;
	        return stream;
	      },closeStream:function(fd) {
	        FS.streams[fd] = null;
	      },chrdev_stream_ops:{open:function(stream) {
	          var device = FS.getDevice(stream.node.rdev);
	          // override node's stream ops with the device's
	          stream.stream_ops = device.stream_ops;
	          // forward the open call
	          if (stream.stream_ops.open) {
	            stream.stream_ops.open(stream);
	          }
	        },llseek:function() {
	          throw new FS.ErrnoError(70);
	        }},major:function(dev) {
	        return ((dev) >> 8);
	      },minor:function(dev) {
	        return ((dev) & 0xff);
	      },makedev:function(ma, mi) {
	        return ((ma) << 8 | (mi));
	      },registerDevice:function(dev, ops) {
	        FS.devices[dev] = { stream_ops: ops };
	      },getDevice:function(dev) {
	        return FS.devices[dev];
	      },getMounts:function(mount) {
	        var mounts = [];
	        var check = [mount];
	  
	        while (check.length) {
	          var m = check.pop();
	  
	          mounts.push(m);
	  
	          check.push.apply(check, m.mounts);
	        }
	  
	        return mounts;
	      },syncfs:function(populate, callback) {
	        if (typeof(populate) === 'function') {
	          callback = populate;
	          populate = false;
	        }
	  
	        FS.syncFSRequests++;
	  
	        if (FS.syncFSRequests > 1) {
	          err('warning: ' + FS.syncFSRequests + ' FS.syncfs operations in flight at once, probably just doing extra work');
	        }
	  
	        var mounts = FS.getMounts(FS.root.mount);
	        var completed = 0;
	  
	        function doCallback(errCode) {
	          FS.syncFSRequests--;
	          return callback(errCode);
	        }
	  
	        function done(errCode) {
	          if (errCode) {
	            if (!done.errored) {
	              done.errored = true;
	              return doCallback(errCode);
	            }
	            return;
	          }
	          if (++completed >= mounts.length) {
	            doCallback(null);
	          }
	        }  
	        // sync all mounts
	        mounts.forEach(function (mount) {
	          if (!mount.type.syncfs) {
	            return done(null);
	          }
	          mount.type.syncfs(mount, populate, done);
	        });
	      },mount:function(type, opts, mountpoint) {
	        var root = mountpoint === '/';
	        var pseudo = !mountpoint;
	        var node;
	  
	        if (root && FS.root) {
	          throw new FS.ErrnoError(10);
	        } else if (!root && !pseudo) {
	          var lookup = FS.lookupPath(mountpoint, { follow_mount: false });
	  
	          mountpoint = lookup.path;  // use the absolute path
	          node = lookup.node;
	  
	          if (FS.isMountpoint(node)) {
	            throw new FS.ErrnoError(10);
	          }
	  
	          if (!FS.isDir(node.mode)) {
	            throw new FS.ErrnoError(54);
	          }
	        }
	  
	        var mount = {
	          type: type,
	          opts: opts,
	          mountpoint: mountpoint,
	          mounts: []
	        };
	  
	        // create a root node for the fs
	        var mountRoot = type.mount(mount);
	        mountRoot.mount = mount;
	        mount.root = mountRoot;
	  
	        if (root) {
	          FS.root = mountRoot;
	        } else if (node) {
	          // set as a mountpoint
	          node.mounted = mount;
	  
	          // add the new mount to the current mount's children
	          if (node.mount) {
	            node.mount.mounts.push(mount);
	          }
	        }
	  
	        return mountRoot;
	      },unmount:function (mountpoint) {
	        var lookup = FS.lookupPath(mountpoint, { follow_mount: false });
	  
	        if (!FS.isMountpoint(lookup.node)) {
	          throw new FS.ErrnoError(28);
	        }
	  
	        // destroy the nodes for this mount, and all its child mounts
	        var node = lookup.node;
	        var mount = node.mounted;
	        var mounts = FS.getMounts(mount);
	  
	        Object.keys(FS.nameTable).forEach(function (hash) {
	          var current = FS.nameTable[hash];
	  
	          while (current) {
	            var next = current.name_next;
	  
	            if (mounts.indexOf(current.mount) !== -1) {
	              FS.destroyNode(current);
	            }
	  
	            current = next;
	          }
	        });
	  
	        // no longer a mountpoint
	        node.mounted = null;
	  
	        // remove this mount from the child mounts
	        var idx = node.mount.mounts.indexOf(mount);
	        node.mount.mounts.splice(idx, 1);
	      },lookup:function(parent, name) {
	        return parent.node_ops.lookup(parent, name);
	      },mknod:function(path, mode, dev) {
	        var lookup = FS.lookupPath(path, { parent: true });
	        var parent = lookup.node;
	        var name = PATH.basename(path);
	        if (!name || name === '.' || name === '..') {
	          throw new FS.ErrnoError(28);
	        }
	        var errCode = FS.mayCreate(parent, name);
	        if (errCode) {
	          throw new FS.ErrnoError(errCode);
	        }
	        if (!parent.node_ops.mknod) {
	          throw new FS.ErrnoError(63);
	        }
	        return parent.node_ops.mknod(parent, name, mode, dev);
	      },create:function(path, mode) {
	        mode = mode !== undefined ? mode : 438 /* 0666 */;
	        mode &= 4095;
	        mode |= 32768;
	        return FS.mknod(path, mode, 0);
	      },mkdir:function(path, mode) {
	        mode = mode !== undefined ? mode : 511 /* 0777 */;
	        mode &= 511 | 512;
	        mode |= 16384;
	        return FS.mknod(path, mode, 0);
	      },mkdirTree:function(path, mode) {
	        var dirs = path.split('/');
	        var d = '';
	        for (var i = 0; i < dirs.length; ++i) {
	          if (!dirs[i]) continue;
	          d += '/' + dirs[i];
	          try {
	            FS.mkdir(d, mode);
	          } catch(e) {
	            if (e.errno != 20) throw e;
	          }
	        }
	      },mkdev:function(path, mode, dev) {
	        if (typeof(dev) === 'undefined') {
	          dev = mode;
	          mode = 438 /* 0666 */;
	        }
	        mode |= 8192;
	        return FS.mknod(path, mode, dev);
	      },symlink:function(oldpath, newpath) {
	        if (!PATH_FS.resolve(oldpath)) {
	          throw new FS.ErrnoError(44);
	        }
	        var lookup = FS.lookupPath(newpath, { parent: true });
	        var parent = lookup.node;
	        if (!parent) {
	          throw new FS.ErrnoError(44);
	        }
	        var newname = PATH.basename(newpath);
	        var errCode = FS.mayCreate(parent, newname);
	        if (errCode) {
	          throw new FS.ErrnoError(errCode);
	        }
	        if (!parent.node_ops.symlink) {
	          throw new FS.ErrnoError(63);
	        }
	        return parent.node_ops.symlink(parent, newname, oldpath);
	      },rename:function(old_path, new_path) {
	        var old_dirname = PATH.dirname(old_path);
	        var new_dirname = PATH.dirname(new_path);
	        var old_name = PATH.basename(old_path);
	        var new_name = PATH.basename(new_path);
	        // parents must exist
	        var lookup, old_dir, new_dir;
	        try {
	          lookup = FS.lookupPath(old_path, { parent: true });
	          old_dir = lookup.node;
	          lookup = FS.lookupPath(new_path, { parent: true });
	          new_dir = lookup.node;
	        } catch (e) {
	          throw new FS.ErrnoError(10);
	        }
	        if (!old_dir || !new_dir) throw new FS.ErrnoError(44);
	        // need to be part of the same mount
	        if (old_dir.mount !== new_dir.mount) {
	          throw new FS.ErrnoError(75);
	        }
	        // source must exist
	        var old_node = FS.lookupNode(old_dir, old_name);
	        // old path should not be an ancestor of the new path
	        var relative = PATH_FS.relative(old_path, new_dirname);
	        if (relative.charAt(0) !== '.') {
	          throw new FS.ErrnoError(28);
	        }
	        // new path should not be an ancestor of the old path
	        relative = PATH_FS.relative(new_path, old_dirname);
	        if (relative.charAt(0) !== '.') {
	          throw new FS.ErrnoError(55);
	        }
	        // see if the new path already exists
	        var new_node;
	        try {
	          new_node = FS.lookupNode(new_dir, new_name);
	        } catch (e) {
	          // not fatal
	        }
	        // early out if nothing needs to change
	        if (old_node === new_node) {
	          return;
	        }
	        // we'll need to delete the old entry
	        var isdir = FS.isDir(old_node.mode);
	        var errCode = FS.mayDelete(old_dir, old_name, isdir);
	        if (errCode) {
	          throw new FS.ErrnoError(errCode);
	        }
	        // need delete permissions if we'll be overwriting.
	        // need create permissions if new doesn't already exist.
	        errCode = new_node ?
	          FS.mayDelete(new_dir, new_name, isdir) :
	          FS.mayCreate(new_dir, new_name);
	        if (errCode) {
	          throw new FS.ErrnoError(errCode);
	        }
	        if (!old_dir.node_ops.rename) {
	          throw new FS.ErrnoError(63);
	        }
	        if (FS.isMountpoint(old_node) || (new_node && FS.isMountpoint(new_node))) {
	          throw new FS.ErrnoError(10);
	        }
	        // if we are going to change the parent, check write permissions
	        if (new_dir !== old_dir) {
	          errCode = FS.nodePermissions(old_dir, 'w');
	          if (errCode) {
	            throw new FS.ErrnoError(errCode);
	          }
	        }
	        try {
	          if (FS.trackingDelegate['willMovePath']) {
	            FS.trackingDelegate['willMovePath'](old_path, new_path);
	          }
	        } catch(e) {
	          err("FS.trackingDelegate['willMovePath']('"+old_path+"', '"+new_path+"') threw an exception: " + e.message);
	        }
	        // remove the node from the lookup hash
	        FS.hashRemoveNode(old_node);
	        // do the underlying fs rename
	        try {
	          old_dir.node_ops.rename(old_node, new_dir, new_name);
	        } catch (e) {
	          throw e;
	        } finally {
	          // add the node back to the hash (in case node_ops.rename
	          // changed its name)
	          FS.hashAddNode(old_node);
	        }
	        try {
	          if (FS.trackingDelegate['onMovePath']) FS.trackingDelegate['onMovePath'](old_path, new_path);
	        } catch(e) {
	          err("FS.trackingDelegate['onMovePath']('"+old_path+"', '"+new_path+"') threw an exception: " + e.message);
	        }
	      },rmdir:function(path) {
	        var lookup = FS.lookupPath(path, { parent: true });
	        var parent = lookup.node;
	        var name = PATH.basename(path);
	        var node = FS.lookupNode(parent, name);
	        var errCode = FS.mayDelete(parent, name, true);
	        if (errCode) {
	          throw new FS.ErrnoError(errCode);
	        }
	        if (!parent.node_ops.rmdir) {
	          throw new FS.ErrnoError(63);
	        }
	        if (FS.isMountpoint(node)) {
	          throw new FS.ErrnoError(10);
	        }
	        try {
	          if (FS.trackingDelegate['willDeletePath']) {
	            FS.trackingDelegate['willDeletePath'](path);
	          }
	        } catch(e) {
	          err("FS.trackingDelegate['willDeletePath']('"+path+"') threw an exception: " + e.message);
	        }
	        parent.node_ops.rmdir(parent, name);
	        FS.destroyNode(node);
	        try {
	          if (FS.trackingDelegate['onDeletePath']) FS.trackingDelegate['onDeletePath'](path);
	        } catch(e) {
	          err("FS.trackingDelegate['onDeletePath']('"+path+"') threw an exception: " + e.message);
	        }
	      },readdir:function(path) {
	        var lookup = FS.lookupPath(path, { follow: true });
	        var node = lookup.node;
	        if (!node.node_ops.readdir) {
	          throw new FS.ErrnoError(54);
	        }
	        return node.node_ops.readdir(node);
	      },unlink:function(path) {
	        var lookup = FS.lookupPath(path, { parent: true });
	        var parent = lookup.node;
	        var name = PATH.basename(path);
	        var node = FS.lookupNode(parent, name);
	        var errCode = FS.mayDelete(parent, name, false);
	        if (errCode) {
	          // According to POSIX, we should map EISDIR to EPERM, but
	          // we instead do what Linux does (and we must, as we use
	          // the musl linux libc).
	          throw new FS.ErrnoError(errCode);
	        }
	        if (!parent.node_ops.unlink) {
	          throw new FS.ErrnoError(63);
	        }
	        if (FS.isMountpoint(node)) {
	          throw new FS.ErrnoError(10);
	        }
	        try {
	          if (FS.trackingDelegate['willDeletePath']) {
	            FS.trackingDelegate['willDeletePath'](path);
	          }
	        } catch(e) {
	          err("FS.trackingDelegate['willDeletePath']('"+path+"') threw an exception: " + e.message);
	        }
	        parent.node_ops.unlink(parent, name);
	        FS.destroyNode(node);
	        try {
	          if (FS.trackingDelegate['onDeletePath']) FS.trackingDelegate['onDeletePath'](path);
	        } catch(e) {
	          err("FS.trackingDelegate['onDeletePath']('"+path+"') threw an exception: " + e.message);
	        }
	      },readlink:function(path) {
	        var lookup = FS.lookupPath(path);
	        var link = lookup.node;
	        if (!link) {
	          throw new FS.ErrnoError(44);
	        }
	        if (!link.node_ops.readlink) {
	          throw new FS.ErrnoError(28);
	        }
	        return PATH_FS.resolve(FS.getPath(link.parent), link.node_ops.readlink(link));
	      },stat:function(path, dontFollow) {
	        var lookup = FS.lookupPath(path, { follow: !dontFollow });
	        var node = lookup.node;
	        if (!node) {
	          throw new FS.ErrnoError(44);
	        }
	        if (!node.node_ops.getattr) {
	          throw new FS.ErrnoError(63);
	        }
	        return node.node_ops.getattr(node);
	      },lstat:function(path) {
	        return FS.stat(path, true);
	      },chmod:function(path, mode, dontFollow) {
	        var node;
	        if (typeof path === 'string') {
	          var lookup = FS.lookupPath(path, { follow: !dontFollow });
	          node = lookup.node;
	        } else {
	          node = path;
	        }
	        if (!node.node_ops.setattr) {
	          throw new FS.ErrnoError(63);
	        }
	        node.node_ops.setattr(node, {
	          mode: (mode & 4095) | (node.mode & ~4095),
	          timestamp: Date.now()
	        });
	      },lchmod:function(path, mode) {
	        FS.chmod(path, mode, true);
	      },fchmod:function(fd, mode) {
	        var stream = FS.getStream(fd);
	        if (!stream) {
	          throw new FS.ErrnoError(8);
	        }
	        FS.chmod(stream.node, mode);
	      },chown:function(path, uid, gid, dontFollow) {
	        var node;
	        if (typeof path === 'string') {
	          var lookup = FS.lookupPath(path, { follow: !dontFollow });
	          node = lookup.node;
	        } else {
	          node = path;
	        }
	        if (!node.node_ops.setattr) {
	          throw new FS.ErrnoError(63);
	        }
	        node.node_ops.setattr(node, {
	          timestamp: Date.now()
	          // we ignore the uid / gid for now
	        });
	      },lchown:function(path, uid, gid) {
	        FS.chown(path, uid, gid, true);
	      },fchown:function(fd, uid, gid) {
	        var stream = FS.getStream(fd);
	        if (!stream) {
	          throw new FS.ErrnoError(8);
	        }
	        FS.chown(stream.node, uid, gid);
	      },truncate:function(path, len) {
	        if (len < 0) {
	          throw new FS.ErrnoError(28);
	        }
	        var node;
	        if (typeof path === 'string') {
	          var lookup = FS.lookupPath(path, { follow: true });
	          node = lookup.node;
	        } else {
	          node = path;
	        }
	        if (!node.node_ops.setattr) {
	          throw new FS.ErrnoError(63);
	        }
	        if (FS.isDir(node.mode)) {
	          throw new FS.ErrnoError(31);
	        }
	        if (!FS.isFile(node.mode)) {
	          throw new FS.ErrnoError(28);
	        }
	        var errCode = FS.nodePermissions(node, 'w');
	        if (errCode) {
	          throw new FS.ErrnoError(errCode);
	        }
	        node.node_ops.setattr(node, {
	          size: len,
	          timestamp: Date.now()
	        });
	      },ftruncate:function(fd, len) {
	        var stream = FS.getStream(fd);
	        if (!stream) {
	          throw new FS.ErrnoError(8);
	        }
	        if ((stream.flags & 2097155) === 0) {
	          throw new FS.ErrnoError(28);
	        }
	        FS.truncate(stream.node, len);
	      },utime:function(path, atime, mtime) {
	        var lookup = FS.lookupPath(path, { follow: true });
	        var node = lookup.node;
	        node.node_ops.setattr(node, {
	          timestamp: Math.max(atime, mtime)
	        });
	      },open:function(path, flags, mode, fd_start, fd_end) {
	        if (path === "") {
	          throw new FS.ErrnoError(44);
	        }
	        flags = typeof flags === 'string' ? FS.modeStringToFlags(flags) : flags;
	        mode = typeof mode === 'undefined' ? 438 /* 0666 */ : mode;
	        if ((flags & 64)) {
	          mode = (mode & 4095) | 32768;
	        } else {
	          mode = 0;
	        }
	        var node;
	        if (typeof path === 'object') {
	          node = path;
	        } else {
	          path = PATH.normalize(path);
	          try {
	            var lookup = FS.lookupPath(path, {
	              follow: !(flags & 131072)
	            });
	            node = lookup.node;
	          } catch (e) {
	            // ignore
	          }
	        }
	        // perhaps we need to create the node
	        var created = false;
	        if ((flags & 64)) {
	          if (node) {
	            // if O_CREAT and O_EXCL are set, error out if the node already exists
	            if ((flags & 128)) {
	              throw new FS.ErrnoError(20);
	            }
	          } else {
	            // node doesn't exist, try to create it
	            node = FS.mknod(path, mode, 0);
	            created = true;
	          }
	        }
	        if (!node) {
	          throw new FS.ErrnoError(44);
	        }
	        // can't truncate a device
	        if (FS.isChrdev(node.mode)) {
	          flags &= ~512;
	        }
	        // if asked only for a directory, then this must be one
	        if ((flags & 65536) && !FS.isDir(node.mode)) {
	          throw new FS.ErrnoError(54);
	        }
	        // check permissions, if this is not a file we just created now (it is ok to
	        // create and write to a file with read-only permissions; it is read-only
	        // for later use)
	        if (!created) {
	          var errCode = FS.mayOpen(node, flags);
	          if (errCode) {
	            throw new FS.ErrnoError(errCode);
	          }
	        }
	        // do truncation if necessary
	        if ((flags & 512)) {
	          FS.truncate(node, 0);
	        }
	        // we've already handled these, don't pass down to the underlying vfs
	        flags &= ~(128 | 512);
	  
	        // register the stream with the filesystem
	        var stream = FS.createStream({
	          node: node,
	          path: FS.getPath(node),  // we want the absolute path to the node
	          flags: flags,
	          seekable: true,
	          position: 0,
	          stream_ops: node.stream_ops,
	          // used by the file family libc calls (fopen, fwrite, ferror, etc.)
	          ungotten: [],
	          error: false
	        }, fd_start, fd_end);
	        // call the new stream's open function
	        if (stream.stream_ops.open) {
	          stream.stream_ops.open(stream);
	        }
	        if (Module['logReadFiles'] && !(flags & 1)) {
	          if (!FS.readFiles) FS.readFiles = {};
	          if (!(path in FS.readFiles)) {
	            FS.readFiles[path] = 1;
	            err("FS.trackingDelegate error on read file: " + path);
	          }
	        }
	        try {
	          if (FS.trackingDelegate['onOpenFile']) {
	            var trackingFlags = 0;
	            if ((flags & 2097155) !== 1) {
	              trackingFlags |= FS.tracking.openFlags.READ;
	            }
	            if ((flags & 2097155) !== 0) {
	              trackingFlags |= FS.tracking.openFlags.WRITE;
	            }
	            FS.trackingDelegate['onOpenFile'](path, trackingFlags);
	          }
	        } catch(e) {
	          err("FS.trackingDelegate['onOpenFile']('"+path+"', flags) threw an exception: " + e.message);
	        }
	        return stream;
	      },close:function(stream) {
	        if (FS.isClosed(stream)) {
	          throw new FS.ErrnoError(8);
	        }
	        if (stream.getdents) stream.getdents = null; // free readdir state
	        try {
	          if (stream.stream_ops.close) {
	            stream.stream_ops.close(stream);
	          }
	        } catch (e) {
	          throw e;
	        } finally {
	          FS.closeStream(stream.fd);
	        }
	        stream.fd = null;
	      },isClosed:function(stream) {
	        return stream.fd === null;
	      },llseek:function(stream, offset, whence) {
	        if (FS.isClosed(stream)) {
	          throw new FS.ErrnoError(8);
	        }
	        if (!stream.seekable || !stream.stream_ops.llseek) {
	          throw new FS.ErrnoError(70);
	        }
	        if (whence != 0 && whence != 1 && whence != 2) {
	          throw new FS.ErrnoError(28);
	        }
	        stream.position = stream.stream_ops.llseek(stream, offset, whence);
	        stream.ungotten = [];
	        return stream.position;
	      },read:function(stream, buffer, offset, length, position) {
	        if (length < 0 || position < 0) {
	          throw new FS.ErrnoError(28);
	        }
	        if (FS.isClosed(stream)) {
	          throw new FS.ErrnoError(8);
	        }
	        if ((stream.flags & 2097155) === 1) {
	          throw new FS.ErrnoError(8);
	        }
	        if (FS.isDir(stream.node.mode)) {
	          throw new FS.ErrnoError(31);
	        }
	        if (!stream.stream_ops.read) {
	          throw new FS.ErrnoError(28);
	        }
	        var seeking = typeof position !== 'undefined';
	        if (!seeking) {
	          position = stream.position;
	        } else if (!stream.seekable) {
	          throw new FS.ErrnoError(70);
	        }
	        var bytesRead = stream.stream_ops.read(stream, buffer, offset, length, position);
	        if (!seeking) stream.position += bytesRead;
	        return bytesRead;
	      },write:function(stream, buffer, offset, length, position, canOwn) {
	        if (length < 0 || position < 0) {
	          throw new FS.ErrnoError(28);
	        }
	        if (FS.isClosed(stream)) {
	          throw new FS.ErrnoError(8);
	        }
	        if ((stream.flags & 2097155) === 0) {
	          throw new FS.ErrnoError(8);
	        }
	        if (FS.isDir(stream.node.mode)) {
	          throw new FS.ErrnoError(31);
	        }
	        if (!stream.stream_ops.write) {
	          throw new FS.ErrnoError(28);
	        }
	        if (stream.flags & 1024) {
	          // seek to the end before writing in append mode
	          FS.llseek(stream, 0, 2);
	        }
	        var seeking = typeof position !== 'undefined';
	        if (!seeking) {
	          position = stream.position;
	        } else if (!stream.seekable) {
	          throw new FS.ErrnoError(70);
	        }
	        var bytesWritten = stream.stream_ops.write(stream, buffer, offset, length, position, canOwn);
	        if (!seeking) stream.position += bytesWritten;
	        try {
	          if (stream.path && FS.trackingDelegate['onWriteToFile']) FS.trackingDelegate['onWriteToFile'](stream.path);
	        } catch(e) {
	          err("FS.trackingDelegate['onWriteToFile']('"+stream.path+"') threw an exception: " + e.message);
	        }
	        return bytesWritten;
	      },allocate:function(stream, offset, length) {
	        if (FS.isClosed(stream)) {
	          throw new FS.ErrnoError(8);
	        }
	        if (offset < 0 || length <= 0) {
	          throw new FS.ErrnoError(28);
	        }
	        if ((stream.flags & 2097155) === 0) {
	          throw new FS.ErrnoError(8);
	        }
	        if (!FS.isFile(stream.node.mode) && !FS.isDir(stream.node.mode)) {
	          throw new FS.ErrnoError(43);
	        }
	        if (!stream.stream_ops.allocate) {
	          throw new FS.ErrnoError(138);
	        }
	        stream.stream_ops.allocate(stream, offset, length);
	      },mmap:function(stream, buffer, offset, length, position, prot, flags) {
	        // User requests writing to file (prot & PROT_WRITE != 0).
	        // Checking if we have permissions to write to the file unless
	        // MAP_PRIVATE flag is set. According to POSIX spec it is possible
	        // to write to file opened in read-only mode with MAP_PRIVATE flag,
	        // as all modifications will be visible only in the memory of
	        // the current process.
	        if ((prot & 2) !== 0
	            && (flags & 2) === 0
	            && (stream.flags & 2097155) !== 2) {
	          throw new FS.ErrnoError(2);
	        }
	        if ((stream.flags & 2097155) === 1) {
	          throw new FS.ErrnoError(2);
	        }
	        if (!stream.stream_ops.mmap) {
	          throw new FS.ErrnoError(43);
	        }
	        return stream.stream_ops.mmap(stream, buffer, offset, length, position, prot, flags);
	      },msync:function(stream, buffer, offset, length, mmapFlags) {
	        if (!stream || !stream.stream_ops.msync) {
	          return 0;
	        }
	        return stream.stream_ops.msync(stream, buffer, offset, length, mmapFlags);
	      },munmap:function(stream) {
	        return 0;
	      },ioctl:function(stream, cmd, arg) {
	        if (!stream.stream_ops.ioctl) {
	          throw new FS.ErrnoError(59);
	        }
	        return stream.stream_ops.ioctl(stream, cmd, arg);
	      },readFile:function(path, opts) {
	        opts = opts || {};
	        opts.flags = opts.flags || 'r';
	        opts.encoding = opts.encoding || 'binary';
	        if (opts.encoding !== 'utf8' && opts.encoding !== 'binary') {
	          throw new Error('Invalid encoding type "' + opts.encoding + '"');
	        }
	        var ret;
	        var stream = FS.open(path, opts.flags);
	        var stat = FS.stat(path);
	        var length = stat.size;
	        var buf = new Uint8Array(length);
	        FS.read(stream, buf, 0, length, 0);
	        if (opts.encoding === 'utf8') {
	          ret = UTF8ArrayToString(buf, 0);
	        } else if (opts.encoding === 'binary') {
	          ret = buf;
	        }
	        FS.close(stream);
	        return ret;
	      },writeFile:function(path, data, opts) {
	        opts = opts || {};
	        opts.flags = opts.flags || 'w';
	        var stream = FS.open(path, opts.flags, opts.mode);
	        if (typeof data === 'string') {
	          var buf = new Uint8Array(lengthBytesUTF8(data)+1);
	          var actualNumBytes = stringToUTF8Array(data, buf, 0, buf.length);
	          FS.write(stream, buf, 0, actualNumBytes, undefined, opts.canOwn);
	        } else if (ArrayBuffer.isView(data)) {
	          FS.write(stream, data, 0, data.byteLength, undefined, opts.canOwn);
	        } else {
	          throw new Error('Unsupported data type');
	        }
	        FS.close(stream);
	      },cwd:function() {
	        return FS.currentPath;
	      },chdir:function(path) {
	        var lookup = FS.lookupPath(path, { follow: true });
	        if (lookup.node === null) {
	          throw new FS.ErrnoError(44);
	        }
	        if (!FS.isDir(lookup.node.mode)) {
	          throw new FS.ErrnoError(54);
	        }
	        var errCode = FS.nodePermissions(lookup.node, 'x');
	        if (errCode) {
	          throw new FS.ErrnoError(errCode);
	        }
	        FS.currentPath = lookup.path;
	      },createDefaultDirectories:function() {
	        FS.mkdir('/tmp');
	        FS.mkdir('/home');
	        FS.mkdir('/home/<USER>');
	      },createDefaultDevices:function() {
	        // create /dev
	        FS.mkdir('/dev');
	        // setup /dev/null
	        FS.registerDevice(FS.makedev(1, 3), {
	          read: function() { return 0; },
	          write: function(stream, buffer, offset, length, pos) { return length; }
	        });
	        FS.mkdev('/dev/null', FS.makedev(1, 3));
	        // setup /dev/tty and /dev/tty1
	        // stderr needs to print output using Module['printErr']
	        // so we register a second tty just for it.
	        TTY.register(FS.makedev(5, 0), TTY.default_tty_ops);
	        TTY.register(FS.makedev(6, 0), TTY.default_tty1_ops);
	        FS.mkdev('/dev/tty', FS.makedev(5, 0));
	        FS.mkdev('/dev/tty1', FS.makedev(6, 0));
	        // setup /dev/[u]random
	        var random_device;
	        if (typeof crypto === 'object' && typeof crypto['getRandomValues'] === 'function') {
	          // for modern web browsers
	          var randomBuffer = new Uint8Array(1);
	          random_device = function() { crypto.getRandomValues(randomBuffer); return randomBuffer[0]; };
	        } else
	        if (ENVIRONMENT_IS_NODE) {
	          // for nodejs with or without crypto support included
	          try {
	            var crypto_module = require$$2__default["default"];
	            // nodejs has crypto support
	            random_device = function() { return crypto_module['randomBytes'](1)[0]; };
	          } catch (e) {
	            // nodejs doesn't have crypto support
	          }
	        } else
	        ;
	        if (!random_device) {
	          // we couldn't find a proper implementation, as Math.random() is not suitable for /dev/random, see emscripten-core/emscripten/pull/7096
	          random_device = function() { abort("random_device"); };
	        }
	        FS.createDevice('/dev', 'random', random_device);
	        FS.createDevice('/dev', 'urandom', random_device);
	        // we're not going to emulate the actual shm device,
	        // just create the tmp dirs that reside in it commonly
	        FS.mkdir('/dev/shm');
	        FS.mkdir('/dev/shm/tmp');
	      },createSpecialDirectories:function() {
	        // create /proc/self/fd which allows /proc/self/fd/6 => readlink gives the name of the stream for fd 6 (see test_unistd_ttyname)
	        FS.mkdir('/proc');
	        FS.mkdir('/proc/self');
	        FS.mkdir('/proc/self/fd');
	        FS.mount({
	          mount: function() {
	            var node = FS.createNode('/proc/self', 'fd', 16384 | 511 /* 0777 */, 73);
	            node.node_ops = {
	              lookup: function(parent, name) {
	                var fd = +name;
	                var stream = FS.getStream(fd);
	                if (!stream) throw new FS.ErrnoError(8);
	                var ret = {
	                  parent: null,
	                  mount: { mountpoint: 'fake' },
	                  node_ops: { readlink: function() { return stream.path } }
	                };
	                ret.parent = ret; // make it look like a simple root node
	                return ret;
	              }
	            };
	            return node;
	          }
	        }, {}, '/proc/self/fd');
	      },createStandardStreams:function() {
	        // TODO deprecate the old functionality of a single
	        // input / output callback and that utilizes FS.createDevice
	        // and instead require a unique set of stream ops
	  
	        // by default, we symlink the standard streams to the
	        // default tty devices. however, if the standard streams
	        // have been overwritten we create a unique device for
	        // them instead.
	        if (Module['stdin']) {
	          FS.createDevice('/dev', 'stdin', Module['stdin']);
	        } else {
	          FS.symlink('/dev/tty', '/dev/stdin');
	        }
	        if (Module['stdout']) {
	          FS.createDevice('/dev', 'stdout', null, Module['stdout']);
	        } else {
	          FS.symlink('/dev/tty', '/dev/stdout');
	        }
	        if (Module['stderr']) {
	          FS.createDevice('/dev', 'stderr', null, Module['stderr']);
	        } else {
	          FS.symlink('/dev/tty1', '/dev/stderr');
	        }
	  
	        // open default streams for the stdin, stdout and stderr devices
	        FS.open('/dev/stdin', 'r');
	        FS.open('/dev/stdout', 'w');
	        FS.open('/dev/stderr', 'w');
	      },ensureErrnoError:function() {
	        if (FS.ErrnoError) return;
	        FS.ErrnoError = function ErrnoError(errno, node) {
	          this.node = node;
	          this.setErrno = function(errno) {
	            this.errno = errno;
	          };
	          this.setErrno(errno);
	          this.message = 'FS error';
	  
	        };
	        FS.ErrnoError.prototype = new Error();
	        FS.ErrnoError.prototype.constructor = FS.ErrnoError;
	        // Some errors may happen quite a bit, to avoid overhead we reuse them (and suffer a lack of stack info)
	        [44].forEach(function(code) {
	          FS.genericErrors[code] = new FS.ErrnoError(code);
	          FS.genericErrors[code].stack = '<generic error, no stack>';
	        });
	      },staticInit:function() {
	        FS.ensureErrnoError();
	  
	        FS.nameTable = new Array(4096);
	  
	        FS.mount(MEMFS, {}, '/');
	  
	        FS.createDefaultDirectories();
	        FS.createDefaultDevices();
	        FS.createSpecialDirectories();
	  
	        FS.filesystems = {
	          'MEMFS': MEMFS,
	        };
	      },init:function(input, output, error) {
	        FS.init.initialized = true;
	  
	        FS.ensureErrnoError();
	  
	        // Allow Module.stdin etc. to provide defaults, if none explicitly passed to us here
	        Module['stdin'] = input || Module['stdin'];
	        Module['stdout'] = output || Module['stdout'];
	        Module['stderr'] = error || Module['stderr'];
	  
	        FS.createStandardStreams();
	      },quit:function() {
	        FS.init.initialized = false;
	        // force-flush all streams, so we get musl std streams printed out
	        var fflush = Module['_fflush'];
	        if (fflush) fflush(0);
	        // close all of our streams
	        for (var i = 0; i < FS.streams.length; i++) {
	          var stream = FS.streams[i];
	          if (!stream) {
	            continue;
	          }
	          FS.close(stream);
	        }
	      },getMode:function(canRead, canWrite) {
	        var mode = 0;
	        if (canRead) mode |= 292 | 73;
	        if (canWrite) mode |= 146;
	        return mode;
	      },joinPath:function(parts, forceRelative) {
	        var path = PATH.join.apply(null, parts);
	        if (forceRelative && path[0] == '/') path = path.substr(1);
	        return path;
	      },absolutePath:function(relative, base) {
	        return PATH_FS.resolve(base, relative);
	      },standardizePath:function(path) {
	        return PATH.normalize(path);
	      },findObject:function(path, dontResolveLastLink) {
	        var ret = FS.analyzePath(path, dontResolveLastLink);
	        if (ret.exists) {
	          return ret.object;
	        } else {
	          ___setErrNo(ret.error);
	          return null;
	        }
	      },analyzePath:function(path, dontResolveLastLink) {
	        // operate from within the context of the symlink's target
	        try {
	          var lookup = FS.lookupPath(path, { follow: !dontResolveLastLink });
	          path = lookup.path;
	        } catch (e) {
	        }
	        var ret = {
	          isRoot: false, exists: false, error: 0, name: null, path: null, object: null,
	          parentExists: false, parentPath: null, parentObject: null
	        };
	        try {
	          var lookup = FS.lookupPath(path, { parent: true });
	          ret.parentExists = true;
	          ret.parentPath = lookup.path;
	          ret.parentObject = lookup.node;
	          ret.name = PATH.basename(path);
	          lookup = FS.lookupPath(path, { follow: !dontResolveLastLink });
	          ret.exists = true;
	          ret.path = lookup.path;
	          ret.object = lookup.node;
	          ret.name = lookup.node.name;
	          ret.isRoot = lookup.path === '/';
	        } catch (e) {
	          ret.error = e.errno;
	        }        return ret;
	      },createFolder:function(parent, name, canRead, canWrite) {
	        var path = PATH.join2(typeof parent === 'string' ? parent : FS.getPath(parent), name);
	        var mode = FS.getMode(canRead, canWrite);
	        return FS.mkdir(path, mode);
	      },createPath:function(parent, path, canRead, canWrite) {
	        parent = typeof parent === 'string' ? parent : FS.getPath(parent);
	        var parts = path.split('/').reverse();
	        while (parts.length) {
	          var part = parts.pop();
	          if (!part) continue;
	          var current = PATH.join2(parent, part);
	          try {
	            FS.mkdir(current);
	          } catch (e) {
	            // ignore EEXIST
	          }
	          parent = current;
	        }
	        return current;
	      },createFile:function(parent, name, properties, canRead, canWrite) {
	        var path = PATH.join2(typeof parent === 'string' ? parent : FS.getPath(parent), name);
	        var mode = FS.getMode(canRead, canWrite);
	        return FS.create(path, mode);
	      },createDataFile:function(parent, name, data, canRead, canWrite, canOwn) {
	        var path = name ? PATH.join2(typeof parent === 'string' ? parent : FS.getPath(parent), name) : parent;
	        var mode = FS.getMode(canRead, canWrite);
	        var node = FS.create(path, mode);
	        if (data) {
	          if (typeof data === 'string') {
	            var arr = new Array(data.length);
	            for (var i = 0, len = data.length; i < len; ++i) arr[i] = data.charCodeAt(i);
	            data = arr;
	          }
	          // make sure we can write to the file
	          FS.chmod(node, mode | 146);
	          var stream = FS.open(node, 'w');
	          FS.write(stream, data, 0, data.length, 0, canOwn);
	          FS.close(stream);
	          FS.chmod(node, mode);
	        }
	        return node;
	      },createDevice:function(parent, name, input, output) {
	        var path = PATH.join2(typeof parent === 'string' ? parent : FS.getPath(parent), name);
	        var mode = FS.getMode(!!input, !!output);
	        if (!FS.createDevice.major) FS.createDevice.major = 64;
	        var dev = FS.makedev(FS.createDevice.major++, 0);
	        // Create a fake device that a set of stream ops to emulate
	        // the old behavior.
	        FS.registerDevice(dev, {
	          open: function(stream) {
	            stream.seekable = false;
	          },
	          close: function(stream) {
	            // flush any pending line data
	            if (output && output.buffer && output.buffer.length) {
	              output(10);
	            }
	          },
	          read: function(stream, buffer, offset, length, pos /* ignored */) {
	            var bytesRead = 0;
	            for (var i = 0; i < length; i++) {
	              var result;
	              try {
	                result = input();
	              } catch (e) {
	                throw new FS.ErrnoError(29);
	              }
	              if (result === undefined && bytesRead === 0) {
	                throw new FS.ErrnoError(6);
	              }
	              if (result === null || result === undefined) break;
	              bytesRead++;
	              buffer[offset+i] = result;
	            }
	            if (bytesRead) {
	              stream.node.timestamp = Date.now();
	            }
	            return bytesRead;
	          },
	          write: function(stream, buffer, offset, length, pos) {
	            for (var i = 0; i < length; i++) {
	              try {
	                output(buffer[offset+i]);
	              } catch (e) {
	                throw new FS.ErrnoError(29);
	              }
	            }
	            if (length) {
	              stream.node.timestamp = Date.now();
	            }
	            return i;
	          }
	        });
	        return FS.mkdev(path, mode, dev);
	      },createLink:function(parent, name, target, canRead, canWrite) {
	        var path = PATH.join2(typeof parent === 'string' ? parent : FS.getPath(parent), name);
	        return FS.symlink(target, path);
	      },forceLoadFile:function(obj) {
	        if (obj.isDevice || obj.isFolder || obj.link || obj.contents) return true;
	        var success = true;
	        if (typeof XMLHttpRequest !== 'undefined') {
	          throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");
	        } else if (read_) {
	          // Command-line.
	          try {
	            // WARNING: Can't read binary files in V8's d8 or tracemonkey's js, as
	            //          read() will try to parse UTF8.
	            obj.contents = intArrayFromString(read_(obj.url), true);
	            obj.usedBytes = obj.contents.length;
	          } catch (e) {
	            success = false;
	          }
	        } else {
	          throw new Error('Cannot load without read() or XMLHttpRequest.');
	        }
	        if (!success) ___setErrNo(29);
	        return success;
	      },createLazyFile:function(parent, name, url, canRead, canWrite) {
	        // Lazy chunked Uint8Array (implements get and length from Uint8Array). Actual getting is abstracted away for eventual reuse.
	        function LazyUint8Array() {
	          this.lengthKnown = false;
	          this.chunks = []; // Loaded chunks. Index is the chunk number
	        }
	        LazyUint8Array.prototype.get = function LazyUint8Array_get(idx) {
	          if (idx > this.length-1 || idx < 0) {
	            return undefined;
	          }
	          var chunkOffset = idx % this.chunkSize;
	          var chunkNum = (idx / this.chunkSize)|0;
	          return this.getter(chunkNum)[chunkOffset];
	        };
	        LazyUint8Array.prototype.setDataGetter = function LazyUint8Array_setDataGetter(getter) {
	          this.getter = getter;
	        };
	        LazyUint8Array.prototype.cacheLength = function LazyUint8Array_cacheLength() {
	          // Find length
	          var xhr = new XMLHttpRequest();
	          xhr.open('HEAD', url, false);
	          xhr.send(null);
	          if (!(xhr.status >= 200 && xhr.status < 300 || xhr.status === 304)) throw new Error("Couldn't load " + url + ". Status: " + xhr.status);
	          var datalength = Number(xhr.getResponseHeader("Content-length"));
	          var header;
	          var hasByteServing = (header = xhr.getResponseHeader("Accept-Ranges")) && header === "bytes";
	          var usesGzip = (header = xhr.getResponseHeader("Content-Encoding")) && header === "gzip";
	  
	          var chunkSize = 1024*1024; // Chunk size in bytes
	  
	          if (!hasByteServing) chunkSize = datalength;
	  
	          // Function to get a range from the remote URL.
	          var doXHR = (function(from, to) {
	            if (from > to) throw new Error("invalid range (" + from + ", " + to + ") or no bytes requested!");
	            if (to > datalength-1) throw new Error("only " + datalength + " bytes available! programmer error!");
	  
	            // TODO: Use mozResponseArrayBuffer, responseStream, etc. if available.
	            var xhr = new XMLHttpRequest();
	            xhr.open('GET', url, false);
	            if (datalength !== chunkSize) xhr.setRequestHeader("Range", "bytes=" + from + "-" + to);
	  
	            // Some hints to the browser that we want binary data.
	            if (typeof Uint8Array != 'undefined') xhr.responseType = 'arraybuffer';
	            if (xhr.overrideMimeType) {
	              xhr.overrideMimeType('text/plain; charset=x-user-defined');
	            }
	  
	            xhr.send(null);
	            if (!(xhr.status >= 200 && xhr.status < 300 || xhr.status === 304)) throw new Error("Couldn't load " + url + ". Status: " + xhr.status);
	            if (xhr.response !== undefined) {
	              return new Uint8Array(xhr.response || []);
	            } else {
	              return intArrayFromString(xhr.responseText || '', true);
	            }
	          });
	          var lazyArray = this;
	          lazyArray.setDataGetter(function(chunkNum) {
	            var start = chunkNum * chunkSize;
	            var end = (chunkNum+1) * chunkSize - 1; // including this byte
	            end = Math.min(end, datalength-1); // if datalength-1 is selected, this is the last block
	            if (typeof(lazyArray.chunks[chunkNum]) === "undefined") {
	              lazyArray.chunks[chunkNum] = doXHR(start, end);
	            }
	            if (typeof(lazyArray.chunks[chunkNum]) === "undefined") throw new Error("doXHR failed!");
	            return lazyArray.chunks[chunkNum];
	          });
	  
	          if (usesGzip || !datalength) {
	            // if the server uses gzip or doesn't supply the length, we have to download the whole file to get the (uncompressed) length
	            chunkSize = datalength = 1; // this will force getter(0)/doXHR do download the whole file
	            datalength = this.getter(0).length;
	            chunkSize = datalength;
	            out("LazyFiles on gzip forces download of the whole file when length is accessed");
	          }
	  
	          this._length = datalength;
	          this._chunkSize = chunkSize;
	          this.lengthKnown = true;
	        };
	        if (typeof XMLHttpRequest !== 'undefined') {
	          if (!ENVIRONMENT_IS_WORKER) throw 'Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc';
	          var lazyArray = new LazyUint8Array();
	          Object.defineProperties(lazyArray, {
	            length: {
	              get: function() {
	                if(!this.lengthKnown) {
	                  this.cacheLength();
	                }
	                return this._length;
	              }
	            },
	            chunkSize: {
	              get: function() {
	                if(!this.lengthKnown) {
	                  this.cacheLength();
	                }
	                return this._chunkSize;
	              }
	            }
	          });
	  
	          var properties = { isDevice: false, contents: lazyArray };
	        } else {
	          var properties = { isDevice: false, url: url };
	        }
	  
	        var node = FS.createFile(parent, name, properties, canRead, canWrite);
	        // This is a total hack, but I want to get this lazy file code out of the
	        // core of MEMFS. If we want to keep this lazy file concept I feel it should
	        // be its own thin LAZYFS proxying calls to MEMFS.
	        if (properties.contents) {
	          node.contents = properties.contents;
	        } else if (properties.url) {
	          node.contents = null;
	          node.url = properties.url;
	        }
	        // Add a function that defers querying the file size until it is asked the first time.
	        Object.defineProperties(node, {
	          usedBytes: {
	            get: function() { return this.contents.length; }
	          }
	        });
	        // override each stream op with one that tries to force load the lazy file first
	        var stream_ops = {};
	        var keys = Object.keys(node.stream_ops);
	        keys.forEach(function(key) {
	          var fn = node.stream_ops[key];
	          stream_ops[key] = function forceLoadLazyFile() {
	            if (!FS.forceLoadFile(node)) {
	              throw new FS.ErrnoError(29);
	            }
	            return fn.apply(null, arguments);
	          };
	        });
	        // use a custom read function
	        stream_ops.read = function stream_ops_read(stream, buffer, offset, length, position) {
	          if (!FS.forceLoadFile(node)) {
	            throw new FS.ErrnoError(29);
	          }
	          var contents = stream.node.contents;
	          if (position >= contents.length)
	            return 0;
	          var size = Math.min(contents.length - position, length);
	          if (contents.slice) { // normal array
	            for (var i = 0; i < size; i++) {
	              buffer[offset + i] = contents[position + i];
	            }
	          } else {
	            for (var i = 0; i < size; i++) { // LazyUint8Array from sync binary XHR
	              buffer[offset + i] = contents.get(position + i);
	            }
	          }
	          return size;
	        };
	        node.stream_ops = stream_ops;
	        return node;
	      },createPreloadedFile:function(parent, name, url, canRead, canWrite, onload, onerror, dontCreateFile, canOwn, preFinish) {
	        Browser.init(); // XXX perhaps this method should move onto Browser?
	        // TODO we should allow people to just pass in a complete filename instead
	        // of parent and name being that we just join them anyways
	        var fullname = name ? PATH_FS.resolve(PATH.join2(parent, name)) : parent;
	        function processData(byteArray) {
	          function finish(byteArray) {
	            if (preFinish) preFinish();
	            if (!dontCreateFile) {
	              FS.createDataFile(parent, name, byteArray, canRead, canWrite, canOwn);
	            }
	            if (onload) onload();
	            removeRunDependency();
	          }
	          var handled = false;
	          Module['preloadPlugins'].forEach(function(plugin) {
	            if (handled) return;
	            if (plugin['canHandle'](fullname)) {
	              plugin['handle'](byteArray, fullname, finish, function() {
	                if (onerror) onerror();
	                removeRunDependency();
	              });
	              handled = true;
	            }
	          });
	          if (!handled) finish(byteArray);
	        }
	        addRunDependency();
	        if (typeof url == 'string') {
	          Browser.asyncLoad(url, function(byteArray) {
	            processData(byteArray);
	          }, onerror);
	        } else {
	          processData(url);
	        }
	      },indexedDB:function() {
	        return window.indexedDB || window.mozIndexedDB || window.webkitIndexedDB || window.msIndexedDB;
	      },DB_NAME:function() {
	        return 'EM_FS_' + window.location.pathname;
	      },DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:function(paths, onload, onerror) {
	        onload = onload || function(){};
	        onerror = onerror || function(){};
	        var indexedDB = FS.indexedDB();
	        try {
	          var openRequest = indexedDB.open(FS.DB_NAME(), FS.DB_VERSION);
	        } catch (e) {
	          return onerror(e);
	        }
	        openRequest.onupgradeneeded = function openRequest_onupgradeneeded() {
	          out('creating db');
	          var db = openRequest.result;
	          db.createObjectStore(FS.DB_STORE_NAME);
	        };
	        openRequest.onsuccess = function openRequest_onsuccess() {
	          var db = openRequest.result;
	          var transaction = db.transaction([FS.DB_STORE_NAME], 'readwrite');
	          var files = transaction.objectStore(FS.DB_STORE_NAME);
	          var ok = 0, fail = 0, total = paths.length;
	          function finish() {
	            if (fail == 0) onload(); else onerror();
	          }
	          paths.forEach(function(path) {
	            var putRequest = files.put(FS.analyzePath(path).object.contents, path);
	            putRequest.onsuccess = function putRequest_onsuccess() { ok++; if (ok + fail == total) finish(); };
	            putRequest.onerror = function putRequest_onerror() { fail++; if (ok + fail == total) finish(); };
	          });
	          transaction.onerror = onerror;
	        };
	        openRequest.onerror = onerror;
	      },loadFilesFromDB:function(paths, onload, onerror) {
	        onload = onload || function(){};
	        onerror = onerror || function(){};
	        var indexedDB = FS.indexedDB();
	        try {
	          var openRequest = indexedDB.open(FS.DB_NAME(), FS.DB_VERSION);
	        } catch (e) {
	          return onerror(e);
	        }
	        openRequest.onupgradeneeded = onerror; // no database to load from
	        openRequest.onsuccess = function openRequest_onsuccess() {
	          var db = openRequest.result;
	          try {
	            var transaction = db.transaction([FS.DB_STORE_NAME], 'readonly');
	          } catch(e) {
	            onerror(e);
	            return;
	          }
	          var files = transaction.objectStore(FS.DB_STORE_NAME);
	          var ok = 0, fail = 0, total = paths.length;
	          function finish() {
	            if (fail == 0) onload(); else onerror();
	          }
	          paths.forEach(function(path) {
	            var getRequest = files.get(path);
	            getRequest.onsuccess = function getRequest_onsuccess() {
	              if (FS.analyzePath(path).exists) {
	                FS.unlink(path);
	              }
	              FS.createDataFile(PATH.dirname(path), PATH.basename(path), getRequest.result, true, true, true);
	              ok++;
	              if (ok + fail == total) finish();
	            };
	            getRequest.onerror = function getRequest_onerror() { fail++; if (ok + fail == total) finish(); };
	          });
	          transaction.onerror = onerror;
	        };
	        openRequest.onerror = onerror;
	      }};var SYSCALLS={DEFAULT_POLLMASK:5,mappings:{},umask:511,calculateAt:function(dirfd, path) {
	        if (path[0] !== '/') {
	          // relative path
	          var dir;
	          if (dirfd === -100) {
	            dir = FS.cwd();
	          } else {
	            var dirstream = FS.getStream(dirfd);
	            if (!dirstream) throw new FS.ErrnoError(8);
	            dir = dirstream.path;
	          }
	          path = PATH.join2(dir, path);
	        }
	        return path;
	      },doStat:function(func, path, buf) {
	        try {
	          var stat = func(path);
	        } catch (e) {
	          if (e && e.node && PATH.normalize(path) !== PATH.normalize(FS.getPath(e.node))) {
	            // an error occurred while trying to look up the path; we should just report ENOTDIR
	            return -54;
	          }
	          throw e;
	        }
	        HEAP32[((buf)>>2)]=stat.dev;
	        HEAP32[(((buf)+(4))>>2)]=0;
	        HEAP32[(((buf)+(8))>>2)]=stat.ino;
	        HEAP32[(((buf)+(12))>>2)]=stat.mode;
	        HEAP32[(((buf)+(16))>>2)]=stat.nlink;
	        HEAP32[(((buf)+(20))>>2)]=stat.uid;
	        HEAP32[(((buf)+(24))>>2)]=stat.gid;
	        HEAP32[(((buf)+(28))>>2)]=stat.rdev;
	        HEAP32[(((buf)+(32))>>2)]=0;
	        (tempI64 = [stat.size>>>0,(tempDouble=stat.size,(+(Math_abs(tempDouble))) >= 1.0 ? (tempDouble > 0.0 ? ((Math_min((+(Math_floor((tempDouble)/4294967296.0))), 4294967295.0))|0)>>>0 : (~~((+(Math_ceil((tempDouble - +(((~~(tempDouble)))>>>0))/4294967296.0)))))>>>0) : 0)],HEAP32[(((buf)+(40))>>2)]=tempI64[0],HEAP32[(((buf)+(44))>>2)]=tempI64[1]);
	        HEAP32[(((buf)+(48))>>2)]=4096;
	        HEAP32[(((buf)+(52))>>2)]=stat.blocks;
	        HEAP32[(((buf)+(56))>>2)]=(stat.atime.getTime() / 1000)|0;
	        HEAP32[(((buf)+(60))>>2)]=0;
	        HEAP32[(((buf)+(64))>>2)]=(stat.mtime.getTime() / 1000)|0;
	        HEAP32[(((buf)+(68))>>2)]=0;
	        HEAP32[(((buf)+(72))>>2)]=(stat.ctime.getTime() / 1000)|0;
	        HEAP32[(((buf)+(76))>>2)]=0;
	        (tempI64 = [stat.ino>>>0,(tempDouble=stat.ino,(+(Math_abs(tempDouble))) >= 1.0 ? (tempDouble > 0.0 ? ((Math_min((+(Math_floor((tempDouble)/4294967296.0))), 4294967295.0))|0)>>>0 : (~~((+(Math_ceil((tempDouble - +(((~~(tempDouble)))>>>0))/4294967296.0)))))>>>0) : 0)],HEAP32[(((buf)+(80))>>2)]=tempI64[0],HEAP32[(((buf)+(84))>>2)]=tempI64[1]);
	        return 0;
	      },doMsync:function(addr, stream, len, flags, offset) {
	        var buffer = new Uint8Array(HEAPU8.subarray(addr, addr + len));
	        FS.msync(stream, buffer, offset, len, flags);
	      },doMkdir:function(path, mode) {
	        // remove a trailing slash, if one - /a/b/ has basename of '', but
	        // we want to create b in the context of this function
	        path = PATH.normalize(path);
	        if (path[path.length-1] === '/') path = path.substr(0, path.length-1);
	        FS.mkdir(path, mode, 0);
	        return 0;
	      },doMknod:function(path, mode, dev) {
	        // we don't want this in the JS API as it uses mknod to create all nodes.
	        switch (mode & 61440) {
	          case 32768:
	          case 8192:
	          case 24576:
	          case 4096:
	          case 49152:
	            break;
	          default: return -28;
	        }
	        FS.mknod(path, mode, dev);
	        return 0;
	      },doReadlink:function(path, buf, bufsize) {
	        if (bufsize <= 0) return -28;
	        var ret = FS.readlink(path);
	  
	        var len = Math.min(bufsize, lengthBytesUTF8(ret));
	        var endChar = HEAP8[buf+len];
	        stringToUTF8(ret, buf, bufsize+1);
	        // readlink is one of the rare functions that write out a C string, but does never append a null to the output buffer(!)
	        // stringToUTF8() always appends a null byte, so restore the character under the null byte after the write.
	        HEAP8[buf+len] = endChar;
	  
	        return len;
	      },doAccess:function(path, amode) {
	        if (amode & ~7) {
	          // need a valid mode
	          return -28;
	        }
	        var node;
	        var lookup = FS.lookupPath(path, { follow: true });
	        node = lookup.node;
	        if (!node) {
	          return -44;
	        }
	        var perms = '';
	        if (amode & 4) perms += 'r';
	        if (amode & 2) perms += 'w';
	        if (amode & 1) perms += 'x';
	        if (perms /* otherwise, they've just passed F_OK */ && FS.nodePermissions(node, perms)) {
	          return -2;
	        }
	        return 0;
	      },doDup:function(path, flags, suggestFD) {
	        var suggest = FS.getStream(suggestFD);
	        if (suggest) FS.close(suggest);
	        return FS.open(path, flags, 0, suggestFD, suggestFD).fd;
	      },doReadv:function(stream, iov, iovcnt, offset) {
	        var ret = 0;
	        for (var i = 0; i < iovcnt; i++) {
	          var ptr = HEAP32[(((iov)+(i*8))>>2)];
	          var len = HEAP32[(((iov)+(i*8 + 4))>>2)];
	          var curr = FS.read(stream, HEAP8,ptr, len, offset);
	          if (curr < 0) return -1;
	          ret += curr;
	          if (curr < len) break; // nothing more to read
	        }
	        return ret;
	      },doWritev:function(stream, iov, iovcnt, offset) {
	        var ret = 0;
	        for (var i = 0; i < iovcnt; i++) {
	          var ptr = HEAP32[(((iov)+(i*8))>>2)];
	          var len = HEAP32[(((iov)+(i*8 + 4))>>2)];
	          var curr = FS.write(stream, HEAP8,ptr, len, offset);
	          if (curr < 0) return -1;
	          ret += curr;
	        }
	        return ret;
	      },varargs:0,get:function(varargs) {
	        SYSCALLS.varargs += 4;
	        var ret = HEAP32[(((SYSCALLS.varargs)-(4))>>2)];
	        return ret;
	      },getStr:function() {
	        var ret = UTF8ToString(SYSCALLS.get());
	        return ret;
	      },getStreamFromFD:function(fd) {
	        // TODO: when all syscalls use wasi, can remove the next line
	        if (fd === undefined) fd = SYSCALLS.get();
	        var stream = FS.getStream(fd);
	        if (!stream) throw new FS.ErrnoError(8);
	        return stream;
	      },get64:function() {
	        var low = SYSCALLS.get(); SYSCALLS.get();
	        return low;
	      },getZero:function() {
	        SYSCALLS.get();
	      }};function ___syscall10(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // unlink
	      var path = SYSCALLS.getStr();
	      FS.unlink(path);
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall12(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // chdir
	      var path = SYSCALLS.getStr();
	      FS.chdir(path);
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  
	  var PROCINFO={ppid:1,pid:42,sid:42,pgid:42};function ___syscall132(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // getpgid
	      var pid = SYSCALLS.get();
	      if (pid && pid !== PROCINFO.pid) return -71;
	      return PROCINFO.pgid;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall133(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // fchdir
	      var stream = SYSCALLS.getStreamFromFD();
	      FS.chdir(stream.path);
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall148(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // fdatasync
	      var stream = SYSCALLS.getStreamFromFD();
	      return 0; // we can't do anything synchronously; the in-memory FS is already synced to
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall183(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // getcwd
	      var buf = SYSCALLS.get(), size = SYSCALLS.get();
	      if (size === 0) return -28;
	      var cwd = FS.cwd();
	      var cwdLengthInBytes = lengthBytesUTF8(cwd);
	      if (size < cwdLengthInBytes + 1) return -68;
	      stringToUTF8(cwd, buf, size);
	      return buf;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall193(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // truncate64
	      var path = SYSCALLS.getStr(), zero = SYSCALLS.getZero(), length = SYSCALLS.get64();
	      FS.truncate(path, length);
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall194(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // ftruncate64
	      var fd = SYSCALLS.get(), zero = SYSCALLS.getZero(), length = SYSCALLS.get64();
	      FS.ftruncate(fd, length);
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall195(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // SYS_stat64
	      var path = SYSCALLS.getStr(), buf = SYSCALLS.get();
	      return SYSCALLS.doStat(FS.stat, path, buf);
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall198(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // lchown32
	      var path = SYSCALLS.getStr(), owner = SYSCALLS.get(), group = SYSCALLS.get();
	      FS.chown(path, owner, group); // XXX we ignore the 'l' aspect, and do the same as chown
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  
	  function ___syscall202(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // getgid32
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }function ___syscall199(a0,a1
	  ) {
	  return ___syscall202(a0,a1);
	  }

	  function ___syscall20(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // getpid
	      return PROCINFO.pid;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall200(a0,a1
	  ) {
	  return ___syscall202(a0,a1);
	  }

	  function ___syscall201(a0,a1
	  ) {
	  return ___syscall202(a0,a1);
	  }


	  function ___syscall207(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // fchown32
	      var fd = SYSCALLS.get(), owner = SYSCALLS.get(), group = SYSCALLS.get();
	      FS.fchown(fd, owner, group);
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall212(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // chown32
	      var path = SYSCALLS.getStr(), owner = SYSCALLS.get(), group = SYSCALLS.get();
	      FS.chown(path, owner, group);
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall221(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // fcntl64
	      var stream = SYSCALLS.getStreamFromFD(), cmd = SYSCALLS.get();
	      switch (cmd) {
	        case 0: {
	          var arg = SYSCALLS.get();
	          if (arg < 0) {
	            return -28;
	          }
	          var newStream;
	          newStream = FS.open(stream.path, stream.flags, 0, arg);
	          return newStream.fd;
	        }
	        case 1:
	        case 2:
	          return 0;  // FD_CLOEXEC makes no sense for a single process.
	        case 3:
	          return stream.flags;
	        case 4: {
	          var arg = SYSCALLS.get();
	          stream.flags |= arg;
	          return 0;
	        }
	        case 12:
	        /* case 12: Currently in musl F_GETLK64 has same value as F_GETLK, so omitted to avoid duplicate case blocks. If that changes, uncomment this */ {
	          
	          var arg = SYSCALLS.get();
	          var offset = 0;
	          // We're always unlocked.
	          HEAP16[(((arg)+(offset))>>1)]=2;
	          return 0;
	        }
	        case 13:
	        case 14:
	        /* case 13: Currently in musl F_SETLK64 has same value as F_SETLK, so omitted to avoid duplicate case blocks. If that changes, uncomment this */
	        /* case 14: Currently in musl F_SETLKW64 has same value as F_SETLKW, so omitted to avoid duplicate case blocks. If that changes, uncomment this */
	          
	          
	          return 0; // Pretend that the locking is successful.
	        case 16:
	        case 8:
	          return -28; // These are for sockets. We don't have them fully implemented yet.
	        case 9:
	          // musl trusts getown return values, due to a bug where they must be, as they overlap with errors. just return -1 here, so fnctl() returns that, and we set errno ourselves.
	          ___setErrNo(28);
	          return -1;
	        default: {
	          return -28;
	        }
	      }
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall29(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // pause
	      return -27; // we can't pause
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall3(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // read
	      var stream = SYSCALLS.getStreamFromFD(), buf = SYSCALLS.get(), count = SYSCALLS.get();
	      return FS.read(stream, HEAP8,buf, count);
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall33(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // access
	      var path = SYSCALLS.getStr(), amode = SYSCALLS.get();
	      return SYSCALLS.doAccess(path, amode);
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall330(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // dup3
	      var old = SYSCALLS.getStreamFromFD(), suggestFD = SYSCALLS.get(), flags = SYSCALLS.get();
	      if (old.fd === suggestFD) return -28;
	      return SYSCALLS.doDup(old.path, old.flags, suggestFD);
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall34(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // nice
	      var inc = SYSCALLS.get();
	      return -63; // no meaning to nice for our single-process environment
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall36(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // sync
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall38(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // rename
	      var old_path = SYSCALLS.getStr(), new_path = SYSCALLS.getStr();
	      FS.rename(old_path, new_path);
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall4(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // write
	      var stream = SYSCALLS.getStreamFromFD(), buf = SYSCALLS.get(), count = SYSCALLS.get();
	      return FS.write(stream, HEAP8,buf, count);
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall40(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // rmdir
	      var path = SYSCALLS.getStr();
	      FS.rmdir(path);
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall41(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // dup
	      var old = SYSCALLS.getStreamFromFD();
	      return FS.open(old.path, old.flags, 0).fd;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall5(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // open
	      var pathname = SYSCALLS.getStr(), flags = SYSCALLS.get(), mode = SYSCALLS.get(); // optional TODO
	      var stream = FS.open(pathname, flags, mode);
	      return stream.fd;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall54(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // ioctl
	      var stream = SYSCALLS.getStreamFromFD(), op = SYSCALLS.get();
	      switch (op) {
	        case 21509:
	        case 21505: {
	          if (!stream.tty) return -59;
	          return 0;
	        }
	        case 21510:
	        case 21511:
	        case 21512:
	        case 21506:
	        case 21507:
	        case 21508: {
	          if (!stream.tty) return -59;
	          return 0; // no-op, not actually adjusting terminal settings
	        }
	        case 21519: {
	          if (!stream.tty) return -59;
	          var argp = SYSCALLS.get();
	          HEAP32[((argp)>>2)]=0;
	          return 0;
	        }
	        case 21520: {
	          if (!stream.tty) return -59;
	          return -28; // not supported
	        }
	        case 21531: {
	          var argp = SYSCALLS.get();
	          return FS.ioctl(stream, op, argp);
	        }
	        case 21523: {
	          // TODO: in theory we should write to the winsize struct that gets
	          // passed in, but for now musl doesn't read anything on it
	          if (!stream.tty) return -59;
	          return 0;
	        }
	        case 21524: {
	          // TODO: technically, this ioctl call should change the window size.
	          // but, since emscripten doesn't have any concept of a terminal window
	          // yet, we'll just silently throw it away as we do TIOCGWINSZ
	          if (!stream.tty) return -59;
	          return 0;
	        }
	        default: abort('bad ioctl syscall ' + op);
	      }
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall57(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // setpgid
	      var pid = SYSCALLS.get(), pgid = SYSCALLS.get();
	      if (pid && pid !== PROCINFO.pid) return -71;
	      if (pgid && pgid !== PROCINFO.pgid) return -63;
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall63(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // dup2
	      var old = SYSCALLS.getStreamFromFD(), suggestFD = SYSCALLS.get();
	      if (old.fd === suggestFD) return suggestFD;
	      return SYSCALLS.doDup(old.path, old.flags, suggestFD);
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall64(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // getppid
	      return PROCINFO.ppid;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall66(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // setsid
	      return 0; // no-op
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall83(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // symlink
	      var target = SYSCALLS.getStr(), linkpath = SYSCALLS.getStr();
	      FS.symlink(target, linkpath);
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall85(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // readlink
	      var path = SYSCALLS.getStr(), buf = SYSCALLS.get(), bufsize = SYSCALLS.get();
	      return SYSCALLS.doReadlink(path, buf, bufsize);
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___syscall9(which, varargs) {SYSCALLS.varargs = varargs;
	  try {
	   // link
	      var oldpath = SYSCALLS.get(), newpath = SYSCALLS.get();
	      return -34; // no hardlinks for us
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return -e.errno;
	  }
	  }

	  function ___unlock() {}

	  
	  function _exit(status) {
	      // void _exit(int status);
	      // http://pubs.opengroup.org/onlinepubs/000095399/functions/exit.html
	      exit(status);
	    }function __exit(a0
	  ) {
	  return _exit(a0);
	  }

	  function _abort() {
	      abort();
	    }

	  
	  var __sigalrm_handler=0;function _alarm(seconds) {
	      setTimeout(function() {
	        if (__sigalrm_handler) dynCall_vi(__sigalrm_handler, 0);
	      }, seconds*1000);
	    }

	  
	  var ___tm_formatted=24544;
	  
	  
	  
	  function _tzset() {
	      // TODO: Use (malleable) environment variables instead of system settings.
	      if (_tzset.called) return;
	      _tzset.called = true;
	  
	      // timezone is specified as seconds west of UTC ("The external variable
	      // `timezone` shall be set to the difference, in seconds, between
	      // Coordinated Universal Time (UTC) and local standard time."), the same
	      // as returned by getTimezoneOffset().
	      // See http://pubs.opengroup.org/onlinepubs/009695399/functions/tzset.html
	      HEAP32[((__get_timezone())>>2)]=(new Date()).getTimezoneOffset() * 60;
	  
	      var currentYear = new Date().getFullYear();
	      var winter = new Date(currentYear, 0, 1);
	      var summer = new Date(currentYear, 6, 1);
	      HEAP32[((__get_daylight())>>2)]=Number(winter.getTimezoneOffset() != summer.getTimezoneOffset());
	  
	      function extractZone(date) {
	        var match = date.toTimeString().match(/\(([A-Za-z ]+)\)$/);
	        return match ? match[1] : "GMT";
	      }      var winterName = extractZone(winter);
	      var summerName = extractZone(summer);
	      var winterNamePtr = allocate(intArrayFromString(winterName), 'i8', ALLOC_NORMAL);
	      var summerNamePtr = allocate(intArrayFromString(summerName), 'i8', ALLOC_NORMAL);
	      if (summer.getTimezoneOffset() < winter.getTimezoneOffset()) {
	        // Northern hemisphere
	        HEAP32[((__get_tzname())>>2)]=winterNamePtr;
	        HEAP32[(((__get_tzname())+(4))>>2)]=summerNamePtr;
	      } else {
	        HEAP32[((__get_tzname())>>2)]=summerNamePtr;
	        HEAP32[(((__get_tzname())+(4))>>2)]=winterNamePtr;
	      }
	    }function _mktime(tmPtr) {
	      _tzset();
	      var date = new Date(HEAP32[(((tmPtr)+(20))>>2)] + 1900,
	                          HEAP32[(((tmPtr)+(16))>>2)],
	                          HEAP32[(((tmPtr)+(12))>>2)],
	                          HEAP32[(((tmPtr)+(8))>>2)],
	                          HEAP32[(((tmPtr)+(4))>>2)],
	                          HEAP32[((tmPtr)>>2)],
	                          0);
	  
	      // There's an ambiguous hour when the time goes back; the tm_isdst field is
	      // used to disambiguate it.  Date() basically guesses, so we fix it up if it
	      // guessed wrong, or fill in tm_isdst with the guess if it's -1.
	      var dst = HEAP32[(((tmPtr)+(32))>>2)];
	      var guessedOffset = date.getTimezoneOffset();
	      var start = new Date(date.getFullYear(), 0, 1);
	      var summerOffset = new Date(date.getFullYear(), 6, 1).getTimezoneOffset();
	      var winterOffset = start.getTimezoneOffset();
	      var dstOffset = Math.min(winterOffset, summerOffset); // DST is in December in South
	      if (dst < 0) {
	        // Attention: some regions don't have DST at all.
	        HEAP32[(((tmPtr)+(32))>>2)]=Number(summerOffset != winterOffset && dstOffset == guessedOffset);
	      } else if ((dst > 0) != (dstOffset == guessedOffset)) {
	        var nonDstOffset = Math.max(winterOffset, summerOffset);
	        var trueOffset = dst > 0 ? dstOffset : nonDstOffset;
	        // Don't try setMinutes(date.getMinutes() + ...) -- it's messed up.
	        date.setTime(date.getTime() + (trueOffset - guessedOffset)*60000);
	      }
	  
	      HEAP32[(((tmPtr)+(24))>>2)]=date.getDay();
	      var yday = ((date.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))|0;
	      HEAP32[(((tmPtr)+(28))>>2)]=yday;
	  
	      return (date.getTime() / 1000)|0;
	    }function _asctime_r(tmPtr, buf) {
	      var date = {
	        tm_sec: HEAP32[((tmPtr)>>2)],
	        tm_min: HEAP32[(((tmPtr)+(4))>>2)],
	        tm_hour: HEAP32[(((tmPtr)+(8))>>2)],
	        tm_mday: HEAP32[(((tmPtr)+(12))>>2)],
	        tm_mon: HEAP32[(((tmPtr)+(16))>>2)],
	        tm_year: HEAP32[(((tmPtr)+(20))>>2)],
	        tm_wday: HEAP32[(((tmPtr)+(24))>>2)]
	      };
	      var days = [ "Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat" ];
	      var months = [ "Jan", "Feb", "Mar", "Apr", "May", "Jun",
	                     "Jul", "Aug", "Sep", "Oct", "Nov", "Dec" ];
	      var s = days[date.tm_wday] + ' ' + months[date.tm_mon] +
	          (date.tm_mday < 10 ? '  ' : ' ') + date.tm_mday +
	          (date.tm_hour < 10 ? ' 0' : ' ') + date.tm_hour +
	          (date.tm_min < 10 ? ':0' : ':') + date.tm_min +
	          (date.tm_sec < 10 ? ':0' : ':') + date.tm_sec +
	          ' ' + (1900 + date.tm_year) + "\n";
	  
	      // asctime_r is specced to behave in an undefined manner if the algorithm would attempt
	      // to write out more than 26 bytes (including the null terminator).
	      // See http://pubs.opengroup.org/onlinepubs/9699919799/functions/asctime.html
	      // Our undefined behavior is to truncate the write to at most 26 bytes, including null terminator.
	      stringToUTF8(s, buf, 26);
	      return buf;
	    }function _asctime(tmPtr) {
	      return _asctime_r(tmPtr, ___tm_formatted);
	    }

	  function _chroot(path) {
	      // int chroot(const char *path);
	      // http://pubs.opengroup.org/onlinepubs/7908799/xsh/chroot.html
	      ___setErrNo(2);
	      return -1;
	    }

	  function _clock() {
	      if (_clock.start === undefined) _clock.start = Date.now();
	      return ((Date.now() - _clock.start) * (1000000 / 1000))|0;
	    }

	  
	  var ENV={};function _confstr(name, buf, len) {
	      // size_t confstr(int name, char *buf, size_t len);
	      // http://pubs.opengroup.org/onlinepubs/000095399/functions/confstr.html
	      var value;
	      switch (name) {
	        case 0:
	          value = ENV['PATH'] || '/';
	          break;
	        case 1:
	          // Mimicking glibc.
	          value = 'POSIX_V6_ILP32_OFF32\nPOSIX_V6_ILP32_OFFBIG';
	          break;
	        case 2:
	          // This JS implementation was tested against this glibc version.
	          value = 'glibc 2.14';
	          break;
	        case 3:
	          // We don't support pthreads.
	          value = '';
	          break;
	        case 1118:
	        case 1122:
	        case 1124:
	        case 1125:
	        case 1126:
	        case 1128:
	        case 1129:
	        case 1130:
	          value = '';
	          break;
	        case 1116:
	        case 1117:
	        case 1121:
	          value = '-m32';
	          break;
	        case 1120:
	          value = '-m32 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64';
	          break;
	        default:
	          ___setErrNo(28);
	          return 0;
	      }
	      if (len == 0 || buf == 0) {
	        return value.length + 1;
	      } else {
	        var length = Math.min(len, value.length);
	        for (var i = 0; i < length; i++) {
	          HEAP8[(((buf)+(i))>>0)]=value.charCodeAt(i);
	        }
	        if (len > length) HEAP8[(((buf)+(i++))>>0)]=0;
	        return i;
	      }
	    }

	  
	  var ___tm_current=24480;
	  
	  
	  
	  var ___tm_timezone=(stringToUTF8("GMT", 24528, 4), 24528);function _localtime_r(time, tmPtr) {
	      _tzset();
	      var date = new Date(HEAP32[((time)>>2)]*1000);
	      HEAP32[((tmPtr)>>2)]=date.getSeconds();
	      HEAP32[(((tmPtr)+(4))>>2)]=date.getMinutes();
	      HEAP32[(((tmPtr)+(8))>>2)]=date.getHours();
	      HEAP32[(((tmPtr)+(12))>>2)]=date.getDate();
	      HEAP32[(((tmPtr)+(16))>>2)]=date.getMonth();
	      HEAP32[(((tmPtr)+(20))>>2)]=date.getFullYear()-1900;
	      HEAP32[(((tmPtr)+(24))>>2)]=date.getDay();
	  
	      var start = new Date(date.getFullYear(), 0, 1);
	      var yday = ((date.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))|0;
	      HEAP32[(((tmPtr)+(28))>>2)]=yday;
	      HEAP32[(((tmPtr)+(36))>>2)]=-(date.getTimezoneOffset() * 60);
	  
	      // Attention: DST is in December in South, and some regions don't have DST at all.
	      var summerOffset = new Date(date.getFullYear(), 6, 1).getTimezoneOffset();
	      var winterOffset = start.getTimezoneOffset();
	      var dst = (summerOffset != winterOffset && date.getTimezoneOffset() == Math.min(winterOffset, summerOffset))|0;
	      HEAP32[(((tmPtr)+(32))>>2)]=dst;
	  
	      var zonePtr = HEAP32[(((__get_tzname())+(dst ? 4 : 0))>>2)];
	      HEAP32[(((tmPtr)+(40))>>2)]=zonePtr;
	  
	      return tmPtr;
	    }function _ctime_r(time, buf) {
	      var stack = stackSave();
	      var rv = _asctime_r(_localtime_r(time, stackAlloc(44)), buf);
	      stackRestore(stack);
	      return rv;
	    }function _ctime(timer) {
	      return _ctime_r(timer, ___tm_current);
	    }

	  function _difftime(time1, time0) {
	      return time1 - time0;
	    }

	  function _emscripten_get_heap_size() {
	      return HEAP8.length;
	    }

	  function _emscripten_get_sbrk_ptr() {
	      return 24464;
	    }

	  
	  
	  
	  var setjmpId=0;function _saveSetjmp(env, label, table, size) {
	      // Not particularly fast: slow table lookup of setjmpId to label. But setjmp
	      // prevents relooping anyhow, so slowness is to be expected. And typical case
	      // is 1 setjmp per invocation, or less.
	      env = env|0;
	      label = label|0;
	      table = table|0;
	      size = size|0;
	      var i = 0;
	      setjmpId = (setjmpId+1)|0;
	      HEAP32[((env)>>2)]=setjmpId;
	      while ((i|0) < (size|0)) {
	        if (((HEAP32[(((table)+((i<<3)))>>2)])|0) == 0) {
	          HEAP32[(((table)+((i<<3)))>>2)]=setjmpId;
	          HEAP32[(((table)+((i<<3)+4))>>2)]=label;
	          // prepare next slot
	          HEAP32[(((table)+((i<<3)+8))>>2)]=0;
	          setTempRet0((size) | 0);
	          return table | 0;
	        }
	        i = i+1|0;
	      }
	      // grow the table
	      size = (size*2)|0;
	      table = _realloc(table|0, 8*(size+1|0)|0) | 0;
	      table = _saveSetjmp(env|0, label|0, table|0, size|0) | 0;
	      setTempRet0((size) | 0);
	      return table | 0;
	    }
	  
	  function _testSetjmp(id, table, size) {
	      id = id|0;
	      table = table|0;
	      size = size|0;
	      var i = 0, curr = 0;
	      while ((i|0) < (size|0)) {
	        curr = ((HEAP32[(((table)+((i<<3)))>>2)])|0);
	        if ((curr|0) == 0) break;
	        if ((curr|0) == (id|0)) {
	          return ((HEAP32[(((table)+((i<<3)+4))>>2)])|0);
	        }
	        i = i+1|0;
	      }
	      return 0;
	    }function _longjmp(env, value) {
	      _setThrew(env, value || 1);
	      throw 'longjmp';
	    }function _emscripten_longjmp(env, value) {
	      _longjmp(env, value);
	    }

	  function _emscripten_memcpy_big(dest, src, num) {
	      HEAPU8.set(HEAPU8.subarray(src, src+num), dest);
	    }

	  
	  function abortOnCannotGrowMemory(requestedSize) {
	      abort('OOM');
	    }function _emscripten_resize_heap(requestedSize) {
	      abortOnCannotGrowMemory();
	    }

	  
	  function _emscripten_get_environ() {
	      if (!_emscripten_get_environ.strings) {
	        // Default values.
	        var env = {
	          'USER': 'web_user',
	          'LOGNAME': 'web_user',
	          'PATH': '/',
	          'PWD': '/',
	          'HOME': '/home/<USER>',
	          // Browser language detection #8751
	          'LANG': ((typeof navigator === 'object' && navigator.languages && navigator.languages[0]) || 'C').replace('-', '_') + '.UTF-8',
	          '_': thisProgram
	        };
	        // Apply the user-provided values, if any.
	        for (var x in ENV) {
	          env[x] = ENV[x];
	        }
	        var strings = [];
	        for (var x in env) {
	          strings.push(x + '=' + env[x]);
	        }
	        _emscripten_get_environ.strings = strings;
	      }
	      return _emscripten_get_environ.strings;
	    }function _environ_get(__environ, environ_buf) {
	      var strings = _emscripten_get_environ();
	      var bufSize = 0;
	      strings.forEach(function(string, i) {
	        var ptr = environ_buf + bufSize;
	        HEAP32[(((__environ)+(i * 4))>>2)]=ptr;
	        writeAsciiToMemory(string, ptr);
	        bufSize += string.length + 1;
	      });
	      return 0;
	    }

	  function _environ_sizes_get(penviron_count, penviron_buf_size) {
	      var strings = _emscripten_get_environ();
	      HEAP32[((penviron_count)>>2)]=strings.length;
	      var bufSize = 0;
	      strings.forEach(function(string) {
	        bufSize += string.length + 1;
	      });
	      HEAP32[((penviron_buf_size)>>2)]=bufSize;
	      return 0;
	    }


	  function _fd_close(fd) {try {
	  
	      var stream = SYSCALLS.getStreamFromFD(fd);
	      FS.close(stream);
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return e.errno;
	  }
	  }

	  function _fd_fdstat_get(fd, pbuf) {try {
	  
	      var stream = SYSCALLS.getStreamFromFD(fd);
	      // All character devices are terminals (other things a Linux system would
	      // assume is a character device, like the mouse, we have special APIs for).
	      var type = stream.tty ? 2 :
	                 FS.isDir(stream.mode) ? 3 :
	                 FS.isLink(stream.mode) ? 7 :
	                 4;
	      HEAP8[((pbuf)>>0)]=type;
	      // TODO HEAP16[(((pbuf)+(2))>>1)]=?;
	      // TODO (tempI64 = [?>>>0,(tempDouble=?,(+(Math_abs(tempDouble))) >= 1.0 ? (tempDouble > 0.0 ? ((Math_min((+(Math_floor((tempDouble)/4294967296.0))), 4294967295.0))|0)>>>0 : (~~((+(Math_ceil((tempDouble - +(((~~(tempDouble)))>>>0))/4294967296.0)))))>>>0) : 0)],HEAP32[(((pbuf)+(8))>>2)]=tempI64[0],HEAP32[(((pbuf)+(12))>>2)]=tempI64[1]);
	      // TODO (tempI64 = [?>>>0,(tempDouble=?,(+(Math_abs(tempDouble))) >= 1.0 ? (tempDouble > 0.0 ? ((Math_min((+(Math_floor((tempDouble)/4294967296.0))), 4294967295.0))|0)>>>0 : (~~((+(Math_ceil((tempDouble - +(((~~(tempDouble)))>>>0))/4294967296.0)))))>>>0) : 0)],HEAP32[(((pbuf)+(16))>>2)]=tempI64[0],HEAP32[(((pbuf)+(20))>>2)]=tempI64[1]);
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return e.errno;
	  }
	  }

	  function _fd_read(fd, iov, iovcnt, pnum) {try {
	  
	      var stream = SYSCALLS.getStreamFromFD(fd);
	      var num = SYSCALLS.doReadv(stream, iov, iovcnt);
	      HEAP32[((pnum)>>2)]=num;
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return e.errno;
	  }
	  }

	  function _fd_seek(fd, offset_low, offset_high, whence, newOffset) {try {
	  
	      var stream = SYSCALLS.getStreamFromFD(fd);
	      var HIGH_OFFSET = 0x100000000; // 2^32
	      // use an unsigned operator on low and shift high by 32-bits
	      var offset = offset_high * HIGH_OFFSET + (offset_low >>> 0);
	  
	      var DOUBLE_LIMIT = 0x20000000000000; // 2^53
	      // we also check for equality since DOUBLE_LIMIT + 1 == DOUBLE_LIMIT
	      if (offset <= -DOUBLE_LIMIT || offset >= DOUBLE_LIMIT) {
	        return -61;
	      }
	  
	      FS.llseek(stream, offset, whence);
	      (tempI64 = [stream.position>>>0,(tempDouble=stream.position,(+(Math_abs(tempDouble))) >= 1.0 ? (tempDouble > 0.0 ? ((Math_min((+(Math_floor((tempDouble)/4294967296.0))), 4294967295.0))|0)>>>0 : (~~((+(Math_ceil((tempDouble - +(((~~(tempDouble)))>>>0))/4294967296.0)))))>>>0) : 0)],HEAP32[((newOffset)>>2)]=tempI64[0],HEAP32[(((newOffset)+(4))>>2)]=tempI64[1]);
	      if (stream.getdents && offset === 0 && whence === 0) stream.getdents = null; // reset readdir state
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return e.errno;
	  }
	  }

	  function _fd_sync(fd) {try {
	  
	      var stream = SYSCALLS.getStreamFromFD(fd);
	      if (stream.stream_ops && stream.stream_ops.fsync) {
	        return -stream.stream_ops.fsync(stream);
	      }
	      return 0; // we can't do anything synchronously; the in-memory FS is already synced to
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return e.errno;
	  }
	  }

	  function _fd_write(fd, iov, iovcnt, pnum) {try {
	  
	      var stream = SYSCALLS.getStreamFromFD(fd);
	      var num = SYSCALLS.doWritev(stream, iov, iovcnt);
	      HEAP32[((pnum)>>2)]=num;
	      return 0;
	    } catch (e) {
	    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) abort(e);
	    return e.errno;
	  }
	  }

	  function _fork() {
	      // pid_t fork(void);
	      // http://pubs.opengroup.org/onlinepubs/000095399/functions/fork.html
	      // We don't support multiple processes.
	      ___setErrNo(6);
	      return -1;
	    }

	  function _fpathconf(fildes, name) {
	      // long fpathconf(int fildes, int name);
	      // http://pubs.opengroup.org/onlinepubs/000095399/functions/encrypt.html
	      // NOTE: The first parameter is ignored, so pathconf == fpathconf.
	      // The constants here aren't real values. Just mimicking glibc.
	      switch (name) {
	        case 0:
	          return 32000;
	        case 1:
	        case 2:
	        case 3:
	          return 255;
	        case 4:
	        case 5:
	        case 16:
	        case 17:
	        case 18:
	          return 4096;
	        case 6:
	        case 7:
	        case 20:
	          return 1;
	        case 8:
	          return 0;
	        case 9:
	        case 10:
	        case 11:
	        case 12:
	        case 14:
	        case 15:
	        case 19:
	          return -1;
	        case 13:
	          return 64;
	      }
	      ___setErrNo(28);
	      return -1;
	    }

	  function _getTempRet0() {
	      return (getTempRet0() | 0);
	    }

	  function _getpagesize() {
	      // int getpagesize(void);
	      return 16384;
	    }

	  
	  function _gmtime_r(time, tmPtr) {
	      var date = new Date(HEAP32[((time)>>2)]*1000);
	      HEAP32[((tmPtr)>>2)]=date.getUTCSeconds();
	      HEAP32[(((tmPtr)+(4))>>2)]=date.getUTCMinutes();
	      HEAP32[(((tmPtr)+(8))>>2)]=date.getUTCHours();
	      HEAP32[(((tmPtr)+(12))>>2)]=date.getUTCDate();
	      HEAP32[(((tmPtr)+(16))>>2)]=date.getUTCMonth();
	      HEAP32[(((tmPtr)+(20))>>2)]=date.getUTCFullYear()-1900;
	      HEAP32[(((tmPtr)+(24))>>2)]=date.getUTCDay();
	      HEAP32[(((tmPtr)+(36))>>2)]=0;
	      HEAP32[(((tmPtr)+(32))>>2)]=0;
	      var start = Date.UTC(date.getUTCFullYear(), 0, 1, 0, 0, 0, 0);
	      var yday = ((date.getTime() - start) / (1000 * 60 * 60 * 24))|0;
	      HEAP32[(((tmPtr)+(28))>>2)]=yday;
	      HEAP32[(((tmPtr)+(40))>>2)]=___tm_timezone;
	  
	      return tmPtr;
	    }function _gmtime(time) {
	      return _gmtime_r(time, ___tm_current);
	    }


	  function _localtime(time) {
	      return _localtime_r(time, ___tm_current);
	    }


	  
	  function _usleep(useconds) {
	      // int usleep(useconds_t useconds);
	      // http://pubs.opengroup.org/onlinepubs/000095399/functions/usleep.html
	      // We're single-threaded, so use a busy loop. Super-ugly.
	      var msec = useconds / 1000;
	      if ((ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) && self['performance'] && self['performance']['now']) {
	        var start = self['performance']['now']();
	        while (self['performance']['now']() - start < msec) {
	          // Do nothing.
	        }
	      } else {
	        var start = Date.now();
	      }
	      return 0;
	    }
	  Module["_usleep"] = _usleep;function _nanosleep(rqtp, rmtp) {
	      // int nanosleep(const struct timespec  *rqtp, struct timespec *rmtp);
	      if (rqtp === 0) {
	        ___setErrNo(28);
	        return -1;
	      }
	      var seconds = HEAP32[((rqtp)>>2)];
	      var nanoseconds = HEAP32[(((rqtp)+(4))>>2)];
	      if (nanoseconds < 0 || nanoseconds > 999999999 || seconds < 0) {
	        ___setErrNo(28);
	        return -1;
	      }
	      if (rmtp !== 0) {
	        HEAP32[((rmtp)>>2)]=0;
	        HEAP32[(((rmtp)+(4))>>2)]=0;
	      }
	      return _usleep((seconds * 1e6) + (nanoseconds / 1000));
	    }

	  function _pathconf(
	  ) {
	  return _fpathconf.apply(null, arguments)
	  }


	  function _setTempRet0($i) {
	      setTempRet0(($i) | 0);
	    }

	  function _setitimer() {
	      throw 'setitimer() is not implemented yet';
	    }

	  function _signal(sig, func) {
	      if (sig == 14 /*SIGALRM*/) {
	        __sigalrm_handler = func;
	      }
	      return 0;
	    }

	  
	  function __isLeapYear(year) {
	        return year%4 === 0 && (year%100 !== 0 || year%400 === 0);
	    }
	  
	  function __arraySum(array, index) {
	      var sum = 0;
	      for (var i = 0; i <= index; sum += array[i++]);
	      return sum;
	    }
	  
	  
	  var __MONTH_DAYS_LEAP=[31,29,31,30,31,30,31,31,30,31,30,31];
	  
	  var __MONTH_DAYS_REGULAR=[31,28,31,30,31,30,31,31,30,31,30,31];function __addDays(date, days) {
	      var newDate = new Date(date.getTime());
	      while(days > 0) {
	        var leap = __isLeapYear(newDate.getFullYear());
	        var currentMonth = newDate.getMonth();
	        var daysInCurrentMonth = (leap ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR)[currentMonth];
	  
	        if (days > daysInCurrentMonth-newDate.getDate()) {
	          // we spill over to next month
	          days -= (daysInCurrentMonth-newDate.getDate()+1);
	          newDate.setDate(1);
	          if (currentMonth < 11) {
	            newDate.setMonth(currentMonth+1);
	          } else {
	            newDate.setMonth(0);
	            newDate.setFullYear(newDate.getFullYear()+1);
	          }
	        } else {
	          // we stay in current month
	          newDate.setDate(newDate.getDate()+days);
	          return newDate;
	        }
	      }
	  
	      return newDate;
	    }function _strftime(s, maxsize, format, tm) {
	      // size_t strftime(char *restrict s, size_t maxsize, const char *restrict format, const struct tm *restrict timeptr);
	      // http://pubs.opengroup.org/onlinepubs/009695399/functions/strftime.html
	  
	      var tm_zone = HEAP32[(((tm)+(40))>>2)];
	  
	      var date = {
	        tm_sec: HEAP32[((tm)>>2)],
	        tm_min: HEAP32[(((tm)+(4))>>2)],
	        tm_hour: HEAP32[(((tm)+(8))>>2)],
	        tm_mday: HEAP32[(((tm)+(12))>>2)],
	        tm_mon: HEAP32[(((tm)+(16))>>2)],
	        tm_year: HEAP32[(((tm)+(20))>>2)],
	        tm_wday: HEAP32[(((tm)+(24))>>2)],
	        tm_yday: HEAP32[(((tm)+(28))>>2)],
	        tm_isdst: HEAP32[(((tm)+(32))>>2)],
	        tm_gmtoff: HEAP32[(((tm)+(36))>>2)],
	        tm_zone: tm_zone ? UTF8ToString(tm_zone) : ''
	      };
	  
	      var pattern = UTF8ToString(format);
	  
	      // expand format
	      var EXPANSION_RULES_1 = {
	        '%c': '%a %b %d %H:%M:%S %Y',     // Replaced by the locale's appropriate date and time representation - e.g., Mon Aug  3 14:02:01 2013
	        '%D': '%m/%d/%y',                 // Equivalent to %m / %d / %y
	        '%F': '%Y-%m-%d',                 // Equivalent to %Y - %m - %d
	        '%h': '%b',                       // Equivalent to %b
	        '%r': '%I:%M:%S %p',              // Replaced by the time in a.m. and p.m. notation
	        '%R': '%H:%M',                    // Replaced by the time in 24-hour notation
	        '%T': '%H:%M:%S',                 // Replaced by the time
	        '%x': '%m/%d/%y',                 // Replaced by the locale's appropriate date representation
	        '%X': '%H:%M:%S',                 // Replaced by the locale's appropriate time representation
	        // Modified Conversion Specifiers
	        '%Ec': '%c',                      // Replaced by the locale's alternative appropriate date and time representation.
	        '%EC': '%C',                      // Replaced by the name of the base year (period) in the locale's alternative representation.
	        '%Ex': '%m/%d/%y',                // Replaced by the locale's alternative date representation.
	        '%EX': '%H:%M:%S',                // Replaced by the locale's alternative time representation.
	        '%Ey': '%y',                      // Replaced by the offset from %EC (year only) in the locale's alternative representation.
	        '%EY': '%Y',                      // Replaced by the full alternative year representation.
	        '%Od': '%d',                      // Replaced by the day of the month, using the locale's alternative numeric symbols, filled as needed with leading zeros if there is any alternative symbol for zero; otherwise, with leading <space> characters.
	        '%Oe': '%e',                      // Replaced by the day of the month, using the locale's alternative numeric symbols, filled as needed with leading <space> characters.
	        '%OH': '%H',                      // Replaced by the hour (24-hour clock) using the locale's alternative numeric symbols.
	        '%OI': '%I',                      // Replaced by the hour (12-hour clock) using the locale's alternative numeric symbols.
	        '%Om': '%m',                      // Replaced by the month using the locale's alternative numeric symbols.
	        '%OM': '%M',                      // Replaced by the minutes using the locale's alternative numeric symbols.
	        '%OS': '%S',                      // Replaced by the seconds using the locale's alternative numeric symbols.
	        '%Ou': '%u',                      // Replaced by the weekday as a number in the locale's alternative representation (Monday=1).
	        '%OU': '%U',                      // Replaced by the week number of the year (Sunday as the first day of the week, rules corresponding to %U ) using the locale's alternative numeric symbols.
	        '%OV': '%V',                      // Replaced by the week number of the year (Monday as the first day of the week, rules corresponding to %V ) using the locale's alternative numeric symbols.
	        '%Ow': '%w',                      // Replaced by the number of the weekday (Sunday=0) using the locale's alternative numeric symbols.
	        '%OW': '%W',                      // Replaced by the week number of the year (Monday as the first day of the week) using the locale's alternative numeric symbols.
	        '%Oy': '%y',                      // Replaced by the year (offset from %C ) using the locale's alternative numeric symbols.
	      };
	      for (var rule in EXPANSION_RULES_1) {
	        pattern = pattern.replace(new RegExp(rule, 'g'), EXPANSION_RULES_1[rule]);
	      }
	  
	      var WEEKDAYS = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
	      var MONTHS = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
	  
	      function leadingSomething(value, digits, character) {
	        var str = typeof value === 'number' ? value.toString() : (value || '');
	        while (str.length < digits) {
	          str = character[0]+str;
	        }
	        return str;
	      }
	  
	      function leadingNulls(value, digits) {
	        return leadingSomething(value, digits, '0');
	      }
	  
	      function compareByDay(date1, date2) {
	        function sgn(value) {
	          return value < 0 ? -1 : (value > 0 ? 1 : 0);
	        }
	  
	        var compare;
	        if ((compare = sgn(date1.getFullYear()-date2.getFullYear())) === 0) {
	          if ((compare = sgn(date1.getMonth()-date2.getMonth())) === 0) {
	            compare = sgn(date1.getDate()-date2.getDate());
	          }
	        }
	        return compare;
	      }
	  
	      function getFirstWeekStartDate(janFourth) {
	          switch (janFourth.getDay()) {
	            case 0: // Sunday
	              return new Date(janFourth.getFullYear()-1, 11, 29);
	            case 1: // Monday
	              return janFourth;
	            case 2: // Tuesday
	              return new Date(janFourth.getFullYear(), 0, 3);
	            case 3: // Wednesday
	              return new Date(janFourth.getFullYear(), 0, 2);
	            case 4: // Thursday
	              return new Date(janFourth.getFullYear(), 0, 1);
	            case 5: // Friday
	              return new Date(janFourth.getFullYear()-1, 11, 31);
	            case 6: // Saturday
	              return new Date(janFourth.getFullYear()-1, 11, 30);
	          }
	      }
	  
	      function getWeekBasedYear(date) {
	          var thisDate = __addDays(new Date(date.tm_year+1900, 0, 1), date.tm_yday);
	  
	          var janFourthThisYear = new Date(thisDate.getFullYear(), 0, 4);
	          var janFourthNextYear = new Date(thisDate.getFullYear()+1, 0, 4);
	  
	          var firstWeekStartThisYear = getFirstWeekStartDate(janFourthThisYear);
	          var firstWeekStartNextYear = getFirstWeekStartDate(janFourthNextYear);
	  
	          if (compareByDay(firstWeekStartThisYear, thisDate) <= 0) {
	            // this date is after the start of the first week of this year
	            if (compareByDay(firstWeekStartNextYear, thisDate) <= 0) {
	              return thisDate.getFullYear()+1;
	            } else {
	              return thisDate.getFullYear();
	            }
	          } else {
	            return thisDate.getFullYear()-1;
	          }
	      }
	  
	      var EXPANSION_RULES_2 = {
	        '%a': function(date) {
	          return WEEKDAYS[date.tm_wday].substring(0,3);
	        },
	        '%A': function(date) {
	          return WEEKDAYS[date.tm_wday];
	        },
	        '%b': function(date) {
	          return MONTHS[date.tm_mon].substring(0,3);
	        },
	        '%B': function(date) {
	          return MONTHS[date.tm_mon];
	        },
	        '%C': function(date) {
	          var year = date.tm_year+1900;
	          return leadingNulls((year/100)|0,2);
	        },
	        '%d': function(date) {
	          return leadingNulls(date.tm_mday, 2);
	        },
	        '%e': function(date) {
	          return leadingSomething(date.tm_mday, 2, ' ');
	        },
	        '%g': function(date) {
	          // %g, %G, and %V give values according to the ISO 8601:2000 standard week-based year.
	          // In this system, weeks begin on a Monday and week 1 of the year is the week that includes
	          // January 4th, which is also the week that includes the first Thursday of the year, and
	          // is also the first week that contains at least four days in the year.
	          // If the first Monday of January is the 2nd, 3rd, or 4th, the preceding days are part of
	          // the last week of the preceding year; thus, for Saturday 2nd January 1999,
	          // %G is replaced by 1998 and %V is replaced by 53. If December 29th, 30th,
	          // or 31st is a Monday, it and any following days are part of week 1 of the following year.
	          // Thus, for Tuesday 30th December 1997, %G is replaced by 1998 and %V is replaced by 01.
	  
	          return getWeekBasedYear(date).toString().substring(2);
	        },
	        '%G': function(date) {
	          return getWeekBasedYear(date);
	        },
	        '%H': function(date) {
	          return leadingNulls(date.tm_hour, 2);
	        },
	        '%I': function(date) {
	          var twelveHour = date.tm_hour;
	          if (twelveHour == 0) twelveHour = 12;
	          else if (twelveHour > 12) twelveHour -= 12;
	          return leadingNulls(twelveHour, 2);
	        },
	        '%j': function(date) {
	          // Day of the year (001-366)
	          return leadingNulls(date.tm_mday+__arraySum(__isLeapYear(date.tm_year+1900) ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR, date.tm_mon-1), 3);
	        },
	        '%m': function(date) {
	          return leadingNulls(date.tm_mon+1, 2);
	        },
	        '%M': function(date) {
	          return leadingNulls(date.tm_min, 2);
	        },
	        '%n': function() {
	          return '\n';
	        },
	        '%p': function(date) {
	          if (date.tm_hour >= 0 && date.tm_hour < 12) {
	            return 'AM';
	          } else {
	            return 'PM';
	          }
	        },
	        '%S': function(date) {
	          return leadingNulls(date.tm_sec, 2);
	        },
	        '%t': function() {
	          return '\t';
	        },
	        '%u': function(date) {
	          return date.tm_wday || 7;
	        },
	        '%U': function(date) {
	          // Replaced by the week number of the year as a decimal number [00,53].
	          // The first Sunday of January is the first day of week 1;
	          // days in the new year before this are in week 0. [ tm_year, tm_wday, tm_yday]
	          var janFirst = new Date(date.tm_year+1900, 0, 1);
	          var firstSunday = janFirst.getDay() === 0 ? janFirst : __addDays(janFirst, 7-janFirst.getDay());
	          var endDate = new Date(date.tm_year+1900, date.tm_mon, date.tm_mday);
	  
	          // is target date after the first Sunday?
	          if (compareByDay(firstSunday, endDate) < 0) {
	            // calculate difference in days between first Sunday and endDate
	            var februaryFirstUntilEndMonth = __arraySum(__isLeapYear(endDate.getFullYear()) ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR, endDate.getMonth()-1)-31;
	            var firstSundayUntilEndJanuary = 31-firstSunday.getDate();
	            var days = firstSundayUntilEndJanuary+februaryFirstUntilEndMonth+endDate.getDate();
	            return leadingNulls(Math.ceil(days/7), 2);
	          }
	  
	          return compareByDay(firstSunday, janFirst) === 0 ? '01': '00';
	        },
	        '%V': function(date) {
	          // Replaced by the week number of the year (Monday as the first day of the week)
	          // as a decimal number [01,53]. If the week containing 1 January has four
	          // or more days in the new year, then it is considered week 1.
	          // Otherwise, it is the last week of the previous year, and the next week is week 1.
	          // Both January 4th and the first Thursday of January are always in week 1. [ tm_year, tm_wday, tm_yday]
	          var janFourthThisYear = new Date(date.tm_year+1900, 0, 4);
	          var janFourthNextYear = new Date(date.tm_year+1901, 0, 4);
	  
	          var firstWeekStartThisYear = getFirstWeekStartDate(janFourthThisYear);
	          var firstWeekStartNextYear = getFirstWeekStartDate(janFourthNextYear);
	  
	          var endDate = __addDays(new Date(date.tm_year+1900, 0, 1), date.tm_yday);
	  
	          if (compareByDay(endDate, firstWeekStartThisYear) < 0) {
	            // if given date is before this years first week, then it belongs to the 53rd week of last year
	            return '53';
	          }
	  
	          if (compareByDay(firstWeekStartNextYear, endDate) <= 0) {
	            // if given date is after next years first week, then it belongs to the 01th week of next year
	            return '01';
	          }
	  
	          // given date is in between CW 01..53 of this calendar year
	          var daysDifference;
	          if (firstWeekStartThisYear.getFullYear() < date.tm_year+1900) {
	            // first CW of this year starts last year
	            daysDifference = date.tm_yday+32-firstWeekStartThisYear.getDate();
	          } else {
	            // first CW of this year starts this year
	            daysDifference = date.tm_yday+1-firstWeekStartThisYear.getDate();
	          }
	          return leadingNulls(Math.ceil(daysDifference/7), 2);
	        },
	        '%w': function(date) {
	          return date.tm_wday;
	        },
	        '%W': function(date) {
	          // Replaced by the week number of the year as a decimal number [00,53].
	          // The first Monday of January is the first day of week 1;
	          // days in the new year before this are in week 0. [ tm_year, tm_wday, tm_yday]
	          var janFirst = new Date(date.tm_year, 0, 1);
	          var firstMonday = janFirst.getDay() === 1 ? janFirst : __addDays(janFirst, janFirst.getDay() === 0 ? 1 : 7-janFirst.getDay()+1);
	          var endDate = new Date(date.tm_year+1900, date.tm_mon, date.tm_mday);
	  
	          // is target date after the first Monday?
	          if (compareByDay(firstMonday, endDate) < 0) {
	            var februaryFirstUntilEndMonth = __arraySum(__isLeapYear(endDate.getFullYear()) ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR, endDate.getMonth()-1)-31;
	            var firstMondayUntilEndJanuary = 31-firstMonday.getDate();
	            var days = firstMondayUntilEndJanuary+februaryFirstUntilEndMonth+endDate.getDate();
	            return leadingNulls(Math.ceil(days/7), 2);
	          }
	          return compareByDay(firstMonday, janFirst) === 0 ? '01': '00';
	        },
	        '%y': function(date) {
	          // Replaced by the last two digits of the year as a decimal number [00,99]. [ tm_year]
	          return (date.tm_year+1900).toString().substring(2);
	        },
	        '%Y': function(date) {
	          // Replaced by the year as a decimal number (for example, 1997). [ tm_year]
	          return date.tm_year+1900;
	        },
	        '%z': function(date) {
	          // Replaced by the offset from UTC in the ISO 8601:2000 standard format ( +hhmm or -hhmm ).
	          // For example, "-0430" means 4 hours 30 minutes behind UTC (west of Greenwich).
	          var off = date.tm_gmtoff;
	          var ahead = off >= 0;
	          off = Math.abs(off) / 60;
	          // convert from minutes into hhmm format (which means 60 minutes = 100 units)
	          off = (off / 60)*100 + (off % 60);
	          return (ahead ? '+' : '-') + String("0000" + off).slice(-4);
	        },
	        '%Z': function(date) {
	          return date.tm_zone;
	        },
	        '%%': function() {
	          return '%';
	        }
	      };
	      for (var rule in EXPANSION_RULES_2) {
	        if (pattern.indexOf(rule) >= 0) {
	          pattern = pattern.replace(new RegExp(rule, 'g'), EXPANSION_RULES_2[rule](date));
	        }
	      }
	  
	      var bytes = intArrayFromString(pattern, false);
	      if (bytes.length > maxsize) {
	        return 0;
	      }
	  
	      writeArrayToMemory(bytes, s);
	      return bytes.length-1;
	    }

	  function _strptime(buf, format, tm) {
	      // char *strptime(const char *restrict buf, const char *restrict format, struct tm *restrict tm);
	      // http://pubs.opengroup.org/onlinepubs/009695399/functions/strptime.html
	      var pattern = UTF8ToString(format);
	  
	      // escape special characters
	      // TODO: not sure we really need to escape all of these in JS regexps
	      var SPECIAL_CHARS = '\\!@#$^&*()+=-[]/{}|:<>?,.';
	      for (var i=0, ii=SPECIAL_CHARS.length; i<ii; ++i) {
	        pattern = pattern.replace(new RegExp('\\'+SPECIAL_CHARS[i], 'g'), '\\'+SPECIAL_CHARS[i]);
	      }
	  
	      // reduce number of matchers
	      var EQUIVALENT_MATCHERS = {
	        '%A':  '%a',
	        '%B':  '%b',
	        '%c':  '%a %b %d %H:%M:%S %Y',
	        '%D':  '%m\\/%d\\/%y',
	        '%e':  '%d',
	        '%F':  '%Y-%m-%d',
	        '%h':  '%b',
	        '%R':  '%H\\:%M',
	        '%r':  '%I\\:%M\\:%S\\s%p',
	        '%T':  '%H\\:%M\\:%S',
	        '%x':  '%m\\/%d\\/(?:%y|%Y)',
	        '%X':  '%H\\:%M\\:%S'
	      };
	      for (var matcher in EQUIVALENT_MATCHERS) {
	        pattern = pattern.replace(matcher, EQUIVALENT_MATCHERS[matcher]);
	      }
	  
	      // TODO: take care of locale
	  
	      var DATE_PATTERNS = {
	        /* weeday name */     '%a': '(?:Sun(?:day)?)|(?:Mon(?:day)?)|(?:Tue(?:sday)?)|(?:Wed(?:nesday)?)|(?:Thu(?:rsday)?)|(?:Fri(?:day)?)|(?:Sat(?:urday)?)',
	        /* month name */      '%b': '(?:Jan(?:uary)?)|(?:Feb(?:ruary)?)|(?:Mar(?:ch)?)|(?:Apr(?:il)?)|May|(?:Jun(?:e)?)|(?:Jul(?:y)?)|(?:Aug(?:ust)?)|(?:Sep(?:tember)?)|(?:Oct(?:ober)?)|(?:Nov(?:ember)?)|(?:Dec(?:ember)?)',
	        /* century */         '%C': '\\d\\d',
	        /* day of month */    '%d': '0[1-9]|[1-9](?!\\d)|1\\d|2\\d|30|31',
	        /* hour (24hr) */     '%H': '\\d(?!\\d)|[0,1]\\d|20|21|22|23',
	        /* hour (12hr) */     '%I': '\\d(?!\\d)|0\\d|10|11|12',
	        /* day of year */     '%j': '00[1-9]|0?[1-9](?!\\d)|0?[1-9]\\d(?!\\d)|[1,2]\\d\\d|3[0-6]\\d',
	        /* month */           '%m': '0[1-9]|[1-9](?!\\d)|10|11|12',
	        /* minutes */         '%M': '0\\d|\\d(?!\\d)|[1-5]\\d',
	        /* whitespace */      '%n': '\\s',
	        /* AM/PM */           '%p': 'AM|am|PM|pm|A\\.M\\.|a\\.m\\.|P\\.M\\.|p\\.m\\.',
	        /* seconds */         '%S': '0\\d|\\d(?!\\d)|[1-5]\\d|60',
	        /* week number */     '%U': '0\\d|\\d(?!\\d)|[1-4]\\d|50|51|52|53',
	        /* week number */     '%W': '0\\d|\\d(?!\\d)|[1-4]\\d|50|51|52|53',
	        /* weekday number */  '%w': '[0-6]',
	        /* 2-digit year */    '%y': '\\d\\d',
	        /* 4-digit year */    '%Y': '\\d\\d\\d\\d',
	        /* % */               '%%': '%',
	        /* whitespace */      '%t': '\\s',
	      };
	  
	      var MONTH_NUMBERS = {JAN: 0, FEB: 1, MAR: 2, APR: 3, MAY: 4, JUN: 5, JUL: 6, AUG: 7, SEP: 8, OCT: 9, NOV: 10, DEC: 11};
	      var DAY_NUMBERS_SUN_FIRST = {SUN: 0, MON: 1, TUE: 2, WED: 3, THU: 4, FRI: 5, SAT: 6};
	      var DAY_NUMBERS_MON_FIRST = {MON: 0, TUE: 1, WED: 2, THU: 3, FRI: 4, SAT: 5, SUN: 6};
	  
	      for (var datePattern in DATE_PATTERNS) {
	        pattern = pattern.replace(datePattern, '('+datePattern+DATE_PATTERNS[datePattern]+')');
	      }
	  
	      // take care of capturing groups
	      var capture = [];
	      for (var i=pattern.indexOf('%'); i>=0; i=pattern.indexOf('%')) {
	        capture.push(pattern[i+1]);
	        pattern = pattern.replace(new RegExp('\\%'+pattern[i+1], 'g'), '');
	      }
	  
	      var matches = new RegExp('^'+pattern, "i").exec(UTF8ToString(buf));
	      // out(UTF8ToString(buf)+ ' is matched by '+((new RegExp('^'+pattern)).source)+' into: '+JSON.stringify(matches));
	  
	      function initDate() {
	        function fixup(value, min, max) {
	          return (typeof value !== 'number' || isNaN(value)) ? min : (value>=min ? (value<=max ? value: max): min);
	        }        return {
	          year: fixup(HEAP32[(((tm)+(20))>>2)] + 1900 , 1970, 9999),
	          month: fixup(HEAP32[(((tm)+(16))>>2)], 0, 11),
	          day: fixup(HEAP32[(((tm)+(12))>>2)], 1, 31),
	          hour: fixup(HEAP32[(((tm)+(8))>>2)], 0, 23),
	          min: fixup(HEAP32[(((tm)+(4))>>2)], 0, 59),
	          sec: fixup(HEAP32[((tm)>>2)], 0, 59)
	        };
	      }  
	      if (matches) {
	        var date = initDate();
	        var value;
	  
	        var getMatch = function(symbol) {
	          var pos = capture.indexOf(symbol);
	          // check if symbol appears in regexp
	          if (pos >= 0) {
	            // return matched value or null (falsy!) for non-matches
	            return matches[pos+1];
	          }
	          return;
	        };
	  
	        // seconds
	        if ((value=getMatch('S'))) {
	          date.sec = parseInt(value);
	        }
	  
	        // minutes
	        if ((value=getMatch('M'))) {
	          date.min = parseInt(value);
	        }
	  
	        // hours
	        if ((value=getMatch('H'))) {
	          // 24h clock
	          date.hour = parseInt(value);
	        } else if ((value = getMatch('I'))) {
	          // AM/PM clock
	          var hour = parseInt(value);
	          if ((value=getMatch('p'))) {
	            hour += value.toUpperCase()[0] === 'P' ? 12 : 0;
	          }
	          date.hour = hour;
	        }
	  
	        // year
	        if ((value=getMatch('Y'))) {
	          // parse from four-digit year
	          date.year = parseInt(value);
	        } else if ((value=getMatch('y'))) {
	          // parse from two-digit year...
	          var year = parseInt(value);
	          if ((value=getMatch('C'))) {
	            // ...and century
	            year += parseInt(value)*100;
	          } else {
	            // ...and rule-of-thumb
	            year += year<69 ? 2000 : 1900;
	          }
	          date.year = year;
	        }
	  
	        // month
	        if ((value=getMatch('m'))) {
	          // parse from month number
	          date.month = parseInt(value)-1;
	        } else if ((value=getMatch('b'))) {
	          // parse from month name
	          date.month = MONTH_NUMBERS[value.substring(0,3).toUpperCase()] || 0;
	          // TODO: derive month from day in year+year, week number+day of week+year
	        }
	  
	        // day
	        if ((value=getMatch('d'))) {
	          // get day of month directly
	          date.day = parseInt(value);
	        } else if ((value=getMatch('j'))) {
	          // get day of month from day of year ...
	          var day = parseInt(value);
	          var leapYear = __isLeapYear(date.year);
	          for (var month=0; month<12; ++month) {
	            var daysUntilMonth = __arraySum(leapYear ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR, month-1);
	            if (day<=daysUntilMonth+(leapYear ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR)[month]) {
	              date.day = day-daysUntilMonth;
	            }
	          }
	        } else if ((value=getMatch('a'))) {
	          // get day of month from weekday ...
	          var weekDay = value.substring(0,3).toUpperCase();
	          if ((value=getMatch('U'))) {
	            // ... and week number (Sunday being first day of week)
	            // Week number of the year (Sunday as the first day of the week) as a decimal number [00,53].
	            // All days in a new year preceding the first Sunday are considered to be in week 0.
	            var weekDayNumber = DAY_NUMBERS_SUN_FIRST[weekDay];
	            var weekNumber = parseInt(value);
	  
	            // January 1st
	            var janFirst = new Date(date.year, 0, 1);
	            var endDate;
	            if (janFirst.getDay() === 0) {
	              // Jan 1st is a Sunday, and, hence in the 1st CW
	              endDate = __addDays(janFirst, weekDayNumber+7*(weekNumber-1));
	            } else {
	              // Jan 1st is not a Sunday, and, hence still in the 0th CW
	              endDate = __addDays(janFirst, 7-janFirst.getDay()+weekDayNumber+7*(weekNumber-1));
	            }
	            date.day = endDate.getDate();
	            date.month = endDate.getMonth();
	          } else if ((value=getMatch('W'))) {
	            // ... and week number (Monday being first day of week)
	            // Week number of the year (Monday as the first day of the week) as a decimal number [00,53].
	            // All days in a new year preceding the first Monday are considered to be in week 0.
	            var weekDayNumber = DAY_NUMBERS_MON_FIRST[weekDay];
	            var weekNumber = parseInt(value);
	  
	            // January 1st
	            var janFirst = new Date(date.year, 0, 1);
	            var endDate;
	            if (janFirst.getDay()===1) {
	              // Jan 1st is a Monday, and, hence in the 1st CW
	               endDate = __addDays(janFirst, weekDayNumber+7*(weekNumber-1));
	            } else {
	              // Jan 1st is not a Monday, and, hence still in the 0th CW
	              endDate = __addDays(janFirst, 7-janFirst.getDay()+1+weekDayNumber+7*(weekNumber-1));
	            }
	  
	            date.day = endDate.getDate();
	            date.month = endDate.getMonth();
	          }
	        }
	  
	        /*
	        tm_sec  int seconds after the minute  0-61*
	        tm_min  int minutes after the hour  0-59
	        tm_hour int hours since midnight  0-23
	        tm_mday int day of the month  1-31
	        tm_mon  int months since January  0-11
	        tm_year int years since 1900
	        tm_wday int days since Sunday 0-6
	        tm_yday int days since January 1  0-365
	        tm_isdst  int Daylight Saving Time flag
	        */
	  
	        var fullDate = new Date(date.year, date.month, date.day, date.hour, date.min, date.sec, 0);
	        HEAP32[((tm)>>2)]=fullDate.getSeconds();
	        HEAP32[(((tm)+(4))>>2)]=fullDate.getMinutes();
	        HEAP32[(((tm)+(8))>>2)]=fullDate.getHours();
	        HEAP32[(((tm)+(12))>>2)]=fullDate.getDate();
	        HEAP32[(((tm)+(16))>>2)]=fullDate.getMonth();
	        HEAP32[(((tm)+(20))>>2)]=fullDate.getFullYear()-1900;
	        HEAP32[(((tm)+(24))>>2)]=fullDate.getDay();
	        HEAP32[(((tm)+(28))>>2)]=__arraySum(__isLeapYear(fullDate.getFullYear()) ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR, fullDate.getMonth()-1)+fullDate.getDate()-1;
	        HEAP32[(((tm)+(32))>>2)]=0;
	  
	        // we need to convert the matched sequence into an integer array to take care of UTF-8 characters > 0x7F
	        // TODO: not sure that intArrayFromString handles all unicode characters correctly
	        return buf+intArrayFromString(matches[0]).length-1;
	      }
	  
	      return 0;
	    }

	  function _sysconf(name) {
	      // long sysconf(int name);
	      // http://pubs.opengroup.org/onlinepubs/009695399/functions/sysconf.html
	      switch(name) {
	        case 30: return 16384;
	        case 85:
	          var maxHeapSize = 2*1024*1024*1024 - 65536;
	          maxHeapSize = HEAPU8.length;
	          return maxHeapSize / 16384;
	        case 132:
	        case 133:
	        case 12:
	        case 137:
	        case 138:
	        case 15:
	        case 235:
	        case 16:
	        case 17:
	        case 18:
	        case 19:
	        case 20:
	        case 149:
	        case 13:
	        case 10:
	        case 236:
	        case 153:
	        case 9:
	        case 21:
	        case 22:
	        case 159:
	        case 154:
	        case 14:
	        case 77:
	        case 78:
	        case 139:
	        case 80:
	        case 81:
	        case 82:
	        case 68:
	        case 67:
	        case 164:
	        case 11:
	        case 29:
	        case 47:
	        case 48:
	        case 95:
	        case 52:
	        case 51:
	        case 46:
	          return 200809;
	        case 79:
	          return 0;
	        case 27:
	        case 246:
	        case 127:
	        case 128:
	        case 23:
	        case 24:
	        case 160:
	        case 161:
	        case 181:
	        case 182:
	        case 242:
	        case 183:
	        case 184:
	        case 243:
	        case 244:
	        case 245:
	        case 165:
	        case 178:
	        case 179:
	        case 49:
	        case 50:
	        case 168:
	        case 169:
	        case 175:
	        case 170:
	        case 171:
	        case 172:
	        case 97:
	        case 76:
	        case 32:
	        case 173:
	        case 35:
	          return -1;
	        case 176:
	        case 177:
	        case 7:
	        case 155:
	        case 8:
	        case 157:
	        case 125:
	        case 126:
	        case 92:
	        case 93:
	        case 129:
	        case 130:
	        case 131:
	        case 94:
	        case 91:
	          return 1;
	        case 74:
	        case 60:
	        case 69:
	        case 70:
	        case 4:
	          return 1024;
	        case 31:
	        case 42:
	        case 72:
	          return 32;
	        case 87:
	        case 26:
	        case 33:
	          return 2147483647;
	        case 34:
	        case 1:
	          return 47839;
	        case 38:
	        case 36:
	          return 99;
	        case 43:
	        case 37:
	          return 2048;
	        case 0: return 2097152;
	        case 3: return 65536;
	        case 28: return 32768;
	        case 44: return 32767;
	        case 75: return 16384;
	        case 39: return 1000;
	        case 89: return 700;
	        case 71: return 256;
	        case 40: return 255;
	        case 2: return 100;
	        case 180: return 64;
	        case 25: return 20;
	        case 5: return 16;
	        case 6: return 6;
	        case 73: return 4;
	        case 84: {
	          if (typeof navigator === 'object') return navigator['hardwareConcurrency'] || 1;
	          return 1;
	        }
	      }
	      ___setErrNo(28);
	      return -1;
	    }

	  function _system(command) {
	      // int system(const char *command);
	      // http://pubs.opengroup.org/onlinepubs/000095399/functions/system.html
	      // Can't call external programs.
	      ___setErrNo(6);
	      return -1;
	    }


	  function _time(ptr) {
	      var ret = (Date.now()/1000)|0;
	      if (ptr) {
	        HEAP32[((ptr)>>2)]=ret;
	      }
	      return ret;
	    }

	  function _timegm(tmPtr) {
	      _tzset();
	      var time = Date.UTC(HEAP32[(((tmPtr)+(20))>>2)] + 1900,
	                          HEAP32[(((tmPtr)+(16))>>2)],
	                          HEAP32[(((tmPtr)+(12))>>2)],
	                          HEAP32[(((tmPtr)+(8))>>2)],
	                          HEAP32[(((tmPtr)+(4))>>2)],
	                          HEAP32[((tmPtr)>>2)],
	                          0);
	      var date = new Date(time);
	  
	      HEAP32[(((tmPtr)+(24))>>2)]=date.getUTCDay();
	      var start = Date.UTC(date.getUTCFullYear(), 0, 1, 0, 0, 0, 0);
	      var yday = ((date.getTime() - start) / (1000 * 60 * 60 * 24))|0;
	      HEAP32[(((tmPtr)+(28))>>2)]=yday;
	  
	      return (date.getTime() / 1000)|0;
	    }


	  function _vfork(
	  ) {
	  return _fork.apply(null, arguments)
	  }
	if (ENVIRONMENT_IS_NODE) {
	    _emscripten_get_now = function _emscripten_get_now_actual() {
	      var t = process['hrtime']();
	      return t[0] * 1e3 + t[1] / 1e6;
	    };
	  } else if (typeof dateNow !== 'undefined') {
	    _emscripten_get_now = dateNow;
	  } else _emscripten_get_now = function() { return performance['now'](); };
	FS.staticInit();
	// Copyright 2017 The Emscripten Authors.  All rights reserved.
	// Emscripten is available under two separate licenses, the MIT license and the
	// University of Illinois/NCSA Open Source License.  Both these licenses can be
	// found in the LICENSE file.

	/** @type {function(string, boolean=, number=)} */
	function intArrayFromString(stringy, dontAddNull, length) {
	  var len = length > 0 ? length : lengthBytesUTF8(stringy)+1;
	  var u8array = new Array(len);
	  var numBytesWritten = stringToUTF8Array(stringy, u8array, 0, u8array.length);
	  if (dontAddNull) u8array.length = numBytesWritten;
	  return u8array;
	}

	function intArrayToString(array) {
	  var ret = [];
	  for (var i = 0; i < array.length; i++) {
	    var chr = array[i];
	    if (chr > 0xFF) {
	      chr &= 0xFF;
	    }
	    ret.push(String.fromCharCode(chr));
	  }
	  return ret.join('');
	}


	// Copied from https://github.com/strophe/strophejs/blob/e06d027/src/polyfills.js#L149

	// This code was written by Tyler Akins and has been placed in the
	// public domain.  It would be nice if you left this header intact.
	// Base64 code from Tyler Akins -- http://rumkin.com

	/**
	 * Decodes a base64 string.
	 * @param {String} input The string to decode.
	 */
	var decodeBase64 = typeof atob === 'function' ? atob : function (input) {
	  var keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';

	  var output = '';
	  var chr1, chr2, chr3;
	  var enc1, enc2, enc3, enc4;
	  var i = 0;
	  // remove all characters that are not A-Z, a-z, 0-9, +, /, or =
	  input = input.replace(/[^A-Za-z0-9\+\/\=]/g, '');
	  do {
	    enc1 = keyStr.indexOf(input.charAt(i++));
	    enc2 = keyStr.indexOf(input.charAt(i++));
	    enc3 = keyStr.indexOf(input.charAt(i++));
	    enc4 = keyStr.indexOf(input.charAt(i++));

	    chr1 = (enc1 << 2) | (enc2 >> 4);
	    chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
	    chr3 = ((enc3 & 3) << 6) | enc4;

	    output = output + String.fromCharCode(chr1);

	    if (enc3 !== 64) {
	      output = output + String.fromCharCode(chr2);
	    }
	    if (enc4 !== 64) {
	      output = output + String.fromCharCode(chr3);
	    }
	  } while (i < input.length);
	  return output;
	};

	// Converts a string of base64 into a byte array.
	// Throws error on invalid input.
	function intArrayFromBase64(s) {
	  if (typeof ENVIRONMENT_IS_NODE === 'boolean' && ENVIRONMENT_IS_NODE) {
	    var buf;
	    try {
	      buf = Buffer.from(s, 'base64');
	    } catch (_) {
	      buf = new Buffer(s, 'base64');
	    }
	    return new Uint8Array(buf.buffer, buf.byteOffset, buf.byteLength);
	  }

	  try {
	    var decoded = decodeBase64(s);
	    var bytes = new Uint8Array(decoded.length);
	    for (var i = 0 ; i < decoded.length ; ++i) {
	      bytes[i] = decoded.charCodeAt(i);
	    }
	    return bytes;
	  } catch (_) {
	    throw new Error('Converting base64 string to bytes failed.');
	  }
	}

	// If filename is a base64 data URI, parses and returns data (Buffer on node,
	// Uint8Array otherwise). If filename is not a base64 data URI, returns undefined.
	function tryParseAsDataURI(filename) {
	  if (!isDataURI(filename)) {
	    return;
	  }

	  return intArrayFromBase64(filename.slice(dataURIPrefix.length));
	}
	var asmLibraryArg = { "__assert_fail": ___assert_fail, "__clock_gettime": ___clock_gettime, "__lock": ___lock, "__syscall10": ___syscall10, "__syscall12": ___syscall12, "__syscall132": ___syscall132, "__syscall133": ___syscall133, "__syscall148": ___syscall148, "__syscall183": ___syscall183, "__syscall193": ___syscall193, "__syscall194": ___syscall194, "__syscall195": ___syscall195, "__syscall198": ___syscall198, "__syscall199": ___syscall199, "__syscall20": ___syscall20, "__syscall200": ___syscall200, "__syscall201": ___syscall201, "__syscall202": ___syscall202, "__syscall207": ___syscall207, "__syscall212": ___syscall212, "__syscall221": ___syscall221, "__syscall29": ___syscall29, "__syscall3": ___syscall3, "__syscall33": ___syscall33, "__syscall330": ___syscall330, "__syscall34": ___syscall34, "__syscall36": ___syscall36, "__syscall38": ___syscall38, "__syscall4": ___syscall4, "__syscall40": ___syscall40, "__syscall41": ___syscall41, "__syscall5": ___syscall5, "__syscall54": ___syscall54, "__syscall57": ___syscall57, "__syscall63": ___syscall63, "__syscall64": ___syscall64, "__syscall66": ___syscall66, "__syscall83": ___syscall83, "__syscall85": ___syscall85, "__syscall9": ___syscall9, "__unlock": ___unlock, "_exit": __exit, "abort": _abort, "alarm": _alarm, "asctime": _asctime, "chroot": _chroot, "clock": _clock, "confstr": _confstr, "ctime": _ctime, "difftime": _difftime, "emscripten_get_sbrk_ptr": _emscripten_get_sbrk_ptr, "emscripten_longjmp": _emscripten_longjmp, "emscripten_memcpy_big": _emscripten_memcpy_big, "emscripten_resize_heap": _emscripten_resize_heap, "environ_get": _environ_get, "environ_sizes_get": _environ_sizes_get, "exit": _exit, "fd_close": _fd_close, "fd_fdstat_get": _fd_fdstat_get, "fd_read": _fd_read, "fd_seek": _fd_seek, "fd_sync": _fd_sync, "fd_write": _fd_write, "fork": _fork, "fpathconf": _fpathconf, "getTempRet0": _getTempRet0, "getpagesize": _getpagesize, "gmtime": _gmtime, "gmtime_r": _gmtime_r, "invoke_ii": invoke_ii, "invoke_iii": invoke_iii, "invoke_vi": invoke_vi, "invoke_vii": invoke_vii, "invoke_viii": invoke_viii, "invoke_viiiiiii": invoke_viiiiiii, "localtime": _localtime, "memory": wasmMemory, "mktime": _mktime, "nanosleep": _nanosleep, "pathconf": _pathconf, "saveSetjmp": _saveSetjmp, "setTempRet0": _setTempRet0, "setitimer": _setitimer, "signal": _signal, "strftime": _strftime, "strptime": _strptime, "sysconf": _sysconf, "system": _system, "table": wasmTable, "testSetjmp": _testSetjmp, "time": _time, "timegm": _timegm, "usleep": _usleep, "vfork": _vfork };
	var asm = createWasm();
	Module["asm"] = asm;
	var ___wasm_call_ctors = Module["___wasm_call_ctors"] = function() {
	  return (___wasm_call_ctors = Module["___wasm_call_ctors"] = Module["asm"]["__wasm_call_ctors"]).apply(null, arguments);
	};

	Module["_main"] = function() {
	  return (Module["_main"] = Module["asm"]["main"]).apply(null, arguments);
	};

	var _malloc = Module["_malloc"] = function() {
	  return (_malloc = Module["_malloc"] = Module["asm"]["malloc"]).apply(null, arguments);
	};

	Module["_free"] = function() {
	  return (Module["_free"] = Module["asm"]["free"]).apply(null, arguments);
	};

	Module["___errno_location"] = function() {
	  return (Module["___errno_location"] = Module["asm"]["__errno_location"]).apply(null, arguments);
	};

	var _realloc = Module["_realloc"] = function() {
	  return (_realloc = Module["_realloc"] = Module["asm"]["realloc"]).apply(null, arguments);
	};

	var __get_tzname = Module["__get_tzname"] = function() {
	  return (__get_tzname = Module["__get_tzname"] = Module["asm"]["_get_tzname"]).apply(null, arguments);
	};

	var __get_daylight = Module["__get_daylight"] = function() {
	  return (__get_daylight = Module["__get_daylight"] = Module["asm"]["_get_daylight"]).apply(null, arguments);
	};

	var __get_timezone = Module["__get_timezone"] = function() {
	  return (__get_timezone = Module["__get_timezone"] = Module["asm"]["_get_timezone"]).apply(null, arguments);
	};

	var _setThrew = Module["_setThrew"] = function() {
	  return (_setThrew = Module["_setThrew"] = Module["asm"]["setThrew"]).apply(null, arguments);
	};

	var dynCall_vi = Module["dynCall_vi"] = function() {
	  return (dynCall_vi = Module["dynCall_vi"] = Module["asm"]["dynCall_vi"]).apply(null, arguments);
	};

	var dynCall_vii = Module["dynCall_vii"] = function() {
	  return (dynCall_vii = Module["dynCall_vii"] = Module["asm"]["dynCall_vii"]).apply(null, arguments);
	};

	var dynCall_viii = Module["dynCall_viii"] = function() {
	  return (dynCall_viii = Module["dynCall_viii"] = Module["asm"]["dynCall_viii"]).apply(null, arguments);
	};

	var dynCall_viiiiiii = Module["dynCall_viiiiiii"] = function() {
	  return (dynCall_viiiiiii = Module["dynCall_viiiiiii"] = Module["asm"]["dynCall_viiiiiii"]).apply(null, arguments);
	};

	var dynCall_ii = Module["dynCall_ii"] = function() {
	  return (dynCall_ii = Module["dynCall_ii"] = Module["asm"]["dynCall_ii"]).apply(null, arguments);
	};

	var dynCall_iii = Module["dynCall_iii"] = function() {
	  return (dynCall_iii = Module["dynCall_iii"] = Module["asm"]["dynCall_iii"]).apply(null, arguments);
	};

	var stackSave = Module["stackSave"] = function() {
	  return (stackSave = Module["stackSave"] = Module["asm"]["stackSave"]).apply(null, arguments);
	};

	var stackAlloc = Module["stackAlloc"] = function() {
	  return (stackAlloc = Module["stackAlloc"] = Module["asm"]["stackAlloc"]).apply(null, arguments);
	};

	var stackRestore = Module["stackRestore"] = function() {
	  return (stackRestore = Module["stackRestore"] = Module["asm"]["stackRestore"]).apply(null, arguments);
	};

	Module["__growWasmMemory"] = function() {
	  return (Module["__growWasmMemory"] = Module["asm"]["__growWasmMemory"]).apply(null, arguments);
	};

	Module["dynCall_v"] = function() {
	  return (Module["dynCall_v"] = Module["asm"]["dynCall_v"]).apply(null, arguments);
	};

	Module["dynCall_viiii"] = function() {
	  return (Module["dynCall_viiii"] = Module["asm"]["dynCall_viiii"]).apply(null, arguments);
	};

	Module["dynCall_iiii"] = function() {
	  return (Module["dynCall_iiii"] = Module["asm"]["dynCall_iiii"]).apply(null, arguments);
	};

	Module["dynCall_jiji"] = function() {
	  return (Module["dynCall_jiji"] = Module["asm"]["dynCall_jiji"]).apply(null, arguments);
	};

	Module["dynCall_iidiiii"] = function() {
	  return (Module["dynCall_iidiiii"] = Module["asm"]["dynCall_iidiiii"]).apply(null, arguments);
	};


	function invoke_ii(index,a1) {
	  var sp = stackSave();
	  try {
	    return dynCall_ii(index,a1);
	  } catch(e) {
	    stackRestore(sp);
	    if (e !== e+0 && e !== 'longjmp') throw e;
	    _setThrew(1, 0);
	  }
	}

	function invoke_vii(index,a1,a2) {
	  var sp = stackSave();
	  try {
	    dynCall_vii(index,a1,a2);
	  } catch(e) {
	    stackRestore(sp);
	    if (e !== e+0 && e !== 'longjmp') throw e;
	    _setThrew(1, 0);
	  }
	}

	function invoke_iii(index,a1,a2) {
	  var sp = stackSave();
	  try {
	    return dynCall_iii(index,a1,a2);
	  } catch(e) {
	    stackRestore(sp);
	    if (e !== e+0 && e !== 'longjmp') throw e;
	    _setThrew(1, 0);
	  }
	}

	function invoke_vi(index,a1) {
	  var sp = stackSave();
	  try {
	    dynCall_vi(index,a1);
	  } catch(e) {
	    stackRestore(sp);
	    if (e !== e+0 && e !== 'longjmp') throw e;
	    _setThrew(1, 0);
	  }
	}

	function invoke_viii(index,a1,a2,a3) {
	  var sp = stackSave();
	  try {
	    dynCall_viii(index,a1,a2,a3);
	  } catch(e) {
	    stackRestore(sp);
	    if (e !== e+0 && e !== 'longjmp') throw e;
	    _setThrew(1, 0);
	  }
	}

	function invoke_viiiiiii(index,a1,a2,a3,a4,a5,a6,a7) {
	  var sp = stackSave();
	  try {
	    dynCall_viiiiiii(index,a1,a2,a3,a4,a5,a6,a7);
	  } catch(e) {
	    stackRestore(sp);
	    if (e !== e+0 && e !== 'longjmp') throw e;
	    _setThrew(1, 0);
	  }
	}



	// === Auto-generated postamble setup entry stuff ===

	Module['asm'] = asm;















































































	var calledRun;

	// Modularize mode returns a function, which can be called to
	// create instances. The instances provide a then() method,
	// must like a Promise, that receives a callback. The callback
	// is called when the module is ready to run, with the module
	// as a parameter. (Like a Promise, it also returns the module
	// so you can use the output of .then(..)).
	Module['then'] = function(func) {
	  // We may already be ready to run code at this time. if
	  // so, just queue a call to the callback.
	  if (calledRun) {
	    func(Module);
	  } else {
	    // we are not ready to call then() yet. we must call it
	    // at the same time we would call onRuntimeInitialized.
	    var old = Module['onRuntimeInitialized'];
	    Module['onRuntimeInitialized'] = function() {
	      if (old) old();
	      func(Module);
	    };
	  }
	  return Module;
	};

	/**
	 * @constructor
	 * @this {ExitStatus}
	 */
	function ExitStatus(status) {
	  this.name = "ExitStatus";
	  this.message = "Program terminated with exit(" + status + ")";
	  this.status = status;
	}


	dependenciesFulfilled = function runCaller() {
	  // If run has never been called, and we should call run (INVOKE_RUN is true, and Module.noInitialRun is not false)
	  if (!calledRun) run();
	  if (!calledRun) dependenciesFulfilled = runCaller; // try this again later, after new deps are fulfilled
	};

	function callMain(args) {

	  var entryFunction = Module['_main'];


	  args = args || [];

	  var argc = args.length+1;
	  var argv = stackAlloc((argc + 1) * 4);
	  HEAP32[argv >> 2] = allocateUTF8OnStack(thisProgram);
	  for (var i = 1; i < argc; i++) {
	    HEAP32[(argv >> 2) + i] = allocateUTF8OnStack(args[i - 1]);
	  }
	  HEAP32[(argv >> 2) + argc] = 0;


	  try {


	    var ret = entryFunction(argc, argv);


	    // In PROXY_TO_PTHREAD builds, we should never exit the runtime below, as execution is asynchronously handed
	    // off to a pthread.
	    // if we're not running an evented main loop, it's time to exit
	      exit(ret, /* implicit = */ true);
	  }
	  catch(e) {
	    if (e instanceof ExitStatus) {
	      // exit() throws this once it's done to make sure execution
	      // has been stopped completely
	      return;
	    } else if (e == 'unwind') {
	      // running an evented main loop, don't immediately exit
	      noExitRuntime = true;
	      return;
	    } else {
	      var toLog = e;
	      if (e && typeof e === 'object' && e.stack) {
	        toLog = [e, e.stack];
	      }
	      err('exception thrown: ' + toLog);
	      quit_(1, e);
	    }
	  } finally {
	  }
	}




	/** @type {function(Array=)} */
	function run(args) {
	  args = args || arguments_;

	  if (runDependencies > 0) {
	    return;
	  }


	  preRun();

	  if (runDependencies > 0) return; // a preRun added a dependency, run will be called later

	  function doRun() {
	    // run may have just been called through dependencies being fulfilled just in this very frame,
	    // or while the async setStatus time below was happening
	    if (calledRun) return;
	    calledRun = true;

	    if (ABORT) return;

	    initRuntime();

	    preMain();

	    if (Module['onRuntimeInitialized']) Module['onRuntimeInitialized']();

	    if (shouldRunNow) callMain(args);

	    postRun();
	  }

	  if (Module['setStatus']) {
	    Module['setStatus']('Running...');
	    setTimeout(function() {
	      setTimeout(function() {
	        Module['setStatus']('');
	      }, 1);
	      doRun();
	    }, 1);
	  } else
	  {
	    doRun();
	  }
	}
	Module['run'] = run;


	function exit(status, implicit) {

	  // if this is just main exit-ing implicitly, and the status is 0, then we
	  // don't need to do anything here and can just leave. if the status is
	  // non-zero, though, then we need to report it.
	  // (we may have warned about this earlier, if a situation justifies doing so)
	  if (implicit && noExitRuntime && status === 0) {
	    return;
	  }

	  if (noExitRuntime) ; else {

	    ABORT = true;

	    if (Module['onExit']) Module['onExit'](status);
	  }

	  quit_(status, new ExitStatus(status));
	}

	if (Module['preInit']) {
	  if (typeof Module['preInit'] == 'function') Module['preInit'] = [Module['preInit']];
	  while (Module['preInit'].length > 0) {
	    Module['preInit'].pop()();
	  }
	}

	// shouldRunNow refers to calling main(), not run().
	var shouldRunNow = true;

	if (Module['noInitialRun']) shouldRunNow = false;


	  noExitRuntime = true;

	run();





	// {{MODULE_ADDITIONS}}



	function runc(cstr, consoleWrite) {
	  Module['consoleWrite'] = consoleWrite;
	  FS.writeFile("file.c", cstr);
	  callMain(["file.c"]);
	  return FS.readFile("file.c");
	}

	Module['runc'] = runc;



	  return PicocModule
	}
	);
	})();
	module.exports = PicocModule;
	});

	function runC(cprog, consoleWrite=null) {
	   const pc = picoc();
	   pc.onRuntimeInitialized = () => {
	      pc.runc(cprog, consoleWrite);
	   };
	}

	exports.runC = runC;

	Object.defineProperty(exports, '__esModule', { value: true });

}));
