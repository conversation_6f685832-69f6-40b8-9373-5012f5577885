<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C代码执行测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .code-block {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #45a049;
        }
        .output {
            background: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            min-height: 100px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>C代码执行测试</h1>
        <p>这个页面用于测试picoc-js的C代码执行功能</p>

        <div class="test-section">
            <h3>测试1: 基本输出</h3>
            <div class="code-block">#include &lt;stdio.h&gt;

int main() {
    printf("Hello, World!\n");
    return 0;
}</div>
            <button class="btn" onclick="testBasicOutput()">运行测试1</button>
            <div id="output1" class="output"></div>
        </div>

        <div class="test-section">
            <h3>测试2: 机器人控制函数</h3>
            <div class="code-block">#include &lt;stdio.h&gt;

int main() {
    printf("FORWARD:4:10\n");
    printf("TURN_RIGHT:90\n");
    printf("FORWARD:4:5\n");
    printf("GPP_SAY:1:Hello Robot!\n");
    return 0;
}</div>
            <button class="btn" onclick="testRobotControl()">运行测试2</button>
            <div id="output2" class="output"></div>
        </div>

        <div class="test-section">
            <h3>测试3: 循环控制</h3>
            <div class="code-block">#include &lt;stdio.h&gt;

int main() {
    int i;
    for(i = 0; i < 3; i++) {
        printf("FORWARD:4:5\n");
        printf("TURN_RIGHT:90\n");
    }
    printf("GPP_SAY:1:Square completed!\n");
    return 0;
}</div>
            <button class="btn" onclick="testLoopControl()">运行测试3</button>
            <div id="output3" class="output"></div>
        </div>

        <div class="test-section">
            <h3>测试4: while循环</h3>
            <div class="code-block">#include &lt;stdio.h&gt;

int main() {
    int count = 0;
    while(count < 4) {
        printf("FORWARD:4:10\n");
        printf("TURN_LEFT:90\n");
        count++;
    }
    printf("GPP_SAY:1:While loop done!\n");
    return 0;
}</div>
            <button class="btn" onclick="testWhileLoop()">运行测试4</button>
            <div id="output4" class="output"></div>
        </div>
    </div>

    <script src="./picoc-test/bundle.umd.js"></script>
    <script>
        let picocjs = null;

        // 初始化picoc
        window.addEventListener('load', () => {
            if (window.picocjs) {
                picocjs = window.picocjs;
                console.log('PicoC 加载成功');
            } else {
                console.error('PicoC 加载失败');
            }
        });

        function clearOutput(outputId) {
            document.getElementById(outputId).textContent = '';
        }

        function appendOutput(outputId, text) {
            const output = document.getElementById(outputId);
            output.textContent += text;
            output.scrollTop = output.scrollHeight;
        }

        async function runCCode(code, outputId) {
            if (!picocjs) {
                appendOutput(outputId, 'PicoC 未加载\n');
                return;
            }

            clearOutput(outputId);
            appendOutput(outputId, '开始执行...\n');

            try {
                await picocjs.runC(code, (output) => {
                    appendOutput(outputId, output);
                });
                appendOutput(outputId, '\n执行完成！\n');
            } catch (error) {
                appendOutput(outputId, `\n执行错误: ${error.message}\n`);
            }
        }

        function testBasicOutput() {
            const code = `#include <stdio.h>

int main() {
    printf("Hello, World!\\n");
    return 0;
}`;
            runCCode(code, 'output1');
        }

        function testRobotControl() {
            const code = `#include <stdio.h>

int main() {
    printf("FORWARD:4:10\\n");
    printf("TURN_RIGHT:90\\n");
    printf("FORWARD:4:5\\n");
    printf("GPP_SAY:1:Hello Robot!\\n");
    return 0;
}`;
            runCCode(code, 'output2');
        }

        function testLoopControl() {
            const code = `#include <stdio.h>

int main() {
    int i;
    for(i = 0; i < 3; i++) {
        printf("FORWARD:4:5\\n");
        printf("TURN_RIGHT:90\\n");
    }
    printf("GPP_SAY:1:Square completed!\\n");
    return 0;
}`;
            runCCode(code, 'output3');
        }

        function testWhileLoop() {
            const code = `#include <stdio.h>

int main() {
    int count = 0;
    while(count < 4) {
        printf("FORWARD:4:10\\n");
        printf("TURN_LEFT:90\\n");
        count++;
    }
    printf("GPP_SAY:1:While loop done!\\n");
    return 0;
}`;
            runCCode(code, 'output4');
        }
    </script>
</body>
</html>
