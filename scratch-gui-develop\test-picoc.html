<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PicoC 测试</title>
</head>
<body>
    <h1>PicoC 集成测试</h1>
    <div>
        <h2>测试代码:</h2>
        <pre id="code">
#include &lt;stdio.h&gt;

int main() {
    printf("Hello from PicoC!\n");
    printf("MOTOR_RUN:1:100\n");
    printf("DELAY:1000\n");
    printf("MOTOR_STOP:1\n");
    return 0;
}
        </pre>
    </div>
    
    <button onclick="runTest()">运行测试</button>
    
    <div>
        <h2>输出:</h2>
        <pre id="output"></pre>
    </div>

    <script src="https://unpkg.com/picoc-js@1.0.12/dist/bundle.umd.js"></script>
    <script>
        function runTest() {
            const code = `
#include <stdio.h>

int main() {
    printf("Hello from PicoC!\\n");
    printf("MOTOR_RUN:1:100\\n");
    printf("DELAY:1000\\n");
    printf("MOTOR_STOP:1\\n");
    return 0;
}
            `;
            
            const outputElement = document.getElementById('output');
            outputElement.textContent = '正在执行...\n';
            
            try {
                picocjs.runC(code, (output) => {
                    console.log('PicoC 输出:', output);
                    outputElement.textContent += output;
                    
                    // 解析命令
                    const lines = output.split('\n');
                    lines.forEach(line => {
                        line = line.trim();
                        if (line.startsWith('MOTOR_RUN:')) {
                            console.log('检测到电机运行命令:', line);
                        } else if (line.startsWith('MOTOR_STOP:')) {
                            console.log('检测到电机停止命令:', line);
                        } else if (line.startsWith('DELAY:')) {
                            console.log('检测到延时命令:', line);
                        }
                    });
                });
            } catch (error) {
                console.error('执行失败:', error);
                outputElement.textContent += '\n错误: ' + error.message;
            }
        }
    </script>
</body>
</html>
