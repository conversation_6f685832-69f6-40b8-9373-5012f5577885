[{"id": "gui.backpack.costumeLabel", "description": "Label for costume backpack item", "defaultMessage": "costume"}, {"id": "gui.backpack.soundLabel", "description": "Label for sound backpack item", "defaultMessage": "sound"}, {"id": "gui.backpack.scriptLabel", "description": "Label for script backpack item", "defaultMessage": "script"}, {"id": "gui.backpack.spriteLabel", "description": "Label for sprite backpack item", "defaultMessage": "sprite"}, {"id": "gui.backpack.header", "description": "But<PERSON> to open the backpack", "defaultMessage": "Backpack"}, {"id": "gui.backpack.errorBackpack", "description": "Error backpack message", "defaultMessage": "Error loading backpack"}, {"id": "gui.backpack.loadingBackpack", "description": "Loading backpack message", "defaultMessage": "Loading..."}, {"id": "gui.backpack.more", "description": "Load more from backpack", "defaultMessage": "More"}, {"id": "gui.backpack.emptyBackpack", "description": "Empty backpack message", "defaultMessage": "Backpack is empty"}]