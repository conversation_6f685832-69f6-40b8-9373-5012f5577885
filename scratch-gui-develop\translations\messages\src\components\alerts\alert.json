[{"id": "gui.alerts.lostPeripheralConnection", "description": "Message indicating that an extension peripheral has been disconnected", "defaultMessage": "<PERSON><PERSON><PERSON> lost connection to {extensionName}."}, {"id": "gui.alerts.tryAgain", "description": "But<PERSON> to try saving again", "defaultMessage": "Try Again"}, {"id": "gui.alerts.download", "description": "Button to download project locally", "defaultMessage": "Download"}, {"id": "gui.connection.reconnect", "description": "<PERSON><PERSON> to reconnect the device", "defaultMessage": "Reconnect"}]