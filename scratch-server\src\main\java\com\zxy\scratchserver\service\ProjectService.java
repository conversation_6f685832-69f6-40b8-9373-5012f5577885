package com.zxy.scratchserver.service;

import com.zxy.scratchserver.dto.ProjectDetailResponse;
import com.zxy.scratchserver.dto.ProjectResponse;
import com.zxy.scratchserver.dto.SaveProjectRequest;
import com.zxy.scratchserver.dto.VersionResponse;

import java.util.List;

/**
 * 项目服务接口
 */
public interface ProjectService {

    /**
     * 获取用户的项目列表
     * @param username 用户名
     * @return 项目列表
     */
    List<ProjectResponse> getUserProjects(String username);

    /**
     * 获取项目详情
     * @param projectId 项目ID
     * @param username 当前用户名
     * @return 项目详情
     */
    ProjectDetailResponse getProjectDetail(Long projectId, String username);

    /**
     * 保存项目
     * @param saveProjectRequest 保存项目请求
     * @param username 当前用户名
     * @return 保存后的项目ID
     */
    Long saveProject(SaveProjectRequest saveProjectRequest, String username);

    /**
     * 获取项目的最新版本号
     * @param projectId 项目ID
     * @return 最新版本号
     */
    String getLatestVersionNumber(Long projectId);

    /**
     * 为项目创建新版本
     * @param projectId 项目ID
     * @param username 用户名
     * @return 新版本信息
     */
    VersionResponse createProjectVersion(Long projectId, String username);

    /**
     * 删除项目
     * @param projectId 项目ID
     * @param username 当前用户名
     * @return 是否删除成功
     */
    boolean deleteProject(Long projectId, String username);
}
