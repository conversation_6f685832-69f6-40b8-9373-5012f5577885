<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态加载 PicoC 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .loading {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        textarea {
            width: 100%;
            height: 200px;
            font-family: monospace;
        }
        #output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>动态加载 PicoC 测试</h1>
    
    <div id="status" class="status loading">正在加载 PicoC...</div>
    
    <div>
        <h2>C 代码:</h2>
        <textarea id="codeInput">#include <stdio.h>

int main() {
    printf("Hello from dynamically loaded PicoC!\n");
    printf("MOTOR_RUN:1:100\n");
    printf("DELAY:1000\n");
    printf("MOTOR_STOP:1\n");
    return 0;
}</textarea>
    </div>
    
    <div>
        <button id="runBtn" onclick="runCode()" disabled>运行 C 代码</button>
        <button onclick="clearOutput()">清空输出</button>
    </div>
    
    <div>
        <h2>输出:</h2>
        <div id="output"></div>
    </div>

    <script>
        let picocLoaded = false;
        let picocjs = null;
        
        // 动态加载 PicoC
        function loadPicoC() {
            return new Promise((resolve, reject) => {
                // 检查是否已经加载
                if (window.picocjs) {
                    picocjs = window.picocjs;
                    picocLoaded = true;
                    resolve();
                    return;
                }

                // 动态加载 PicoC 脚本
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/picoc-js@1.0.12/dist/bundle.umd.js';
                script.onload = () => {
                    picocjs = window.picocjs;
                    picocLoaded = true;
                    console.log('PicoC 加载成功');
                    resolve();
                };
                script.onerror = () => {
                    console.error('PicoC 加载失败');
                    reject(new Error('PicoC 加载失败'));
                };
                document.head.appendChild(script);
            });
        }
        
        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function addOutput(text) {
            const output = document.getElementById('output');
            output.textContent += text;
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            document.getElementById('output').textContent = '';
        }
        
        async function runCode() {
            if (!picocLoaded || !picocjs) {
                updateStatus('PicoC 尚未加载完成', 'error');
                return;
            }
            
            const code = document.getElementById('codeInput').value;
            const runBtn = document.getElementById('runBtn');
            
            if (!code.trim()) {
                updateStatus('请输入C代码', 'error');
                return;
            }
            
            runBtn.disabled = true;
            runBtn.textContent = '运行中...';
            updateStatus('正在执行C代码...', 'loading');
            
            addOutput('=== 开始执行 ===\n');
            
            try {
                await picocjs.runC(code, (output) => {
                    console.log('PicoC输出:', output);
                    addOutput(output);
                    
                    // 解析命令
                    const lines = output.split('\n');
                    lines.forEach(line => {
                        line = line.trim();
                        if (line.startsWith('MOTOR_RUN:')) {
                            console.log('检测到电机运行命令:', line);
                        } else if (line.startsWith('MOTOR_STOP:')) {
                            console.log('检测到电机停止命令:', line);
                        } else if (line.startsWith('DELAY:')) {
                            console.log('检测到延时命令:', line);
                        }
                    });
                });
                
                addOutput('\n=== 执行完成 ===\n');
                updateStatus('C代码执行成功！', 'success');
                
            } catch (error) {
                console.error('执行失败:', error);
                addOutput('\n错误: ' + error.message + '\n');
                updateStatus('执行失败: ' + error.message, 'error');
            } finally {
                runBtn.disabled = false;
                runBtn.textContent = '运行 C 代码';
            }
        }
        
        // 页面加载时初始化
        window.addEventListener('load', async () => {
            try {
                await loadPicoC();
                updateStatus('PicoC 加载成功，可以运行C代码了！', 'success');
                document.getElementById('runBtn').disabled = false;
            } catch (error) {
                updateStatus('PicoC 加载失败: ' + error.message, 'error');
            }
        });
    </script>
</body>
</html>
