[{"id": "gui.cCodePanel.title", "description": "标题：C代码面板", "defaultMessage": "C代码"}, {"id": "gui.cCodePanel.loading", "description": "PicoC加载中按钮状态", "defaultMessage": "加载中..."}, {"id": "gui.cCodePanel.compiling", "description": "编译C代码按钮状态", "defaultMessage": "编译中..."}, {"id": "gui.cCodePanel.running", "description": "运行C代码按钮状态", "defaultMessage": "运行中..."}, {"id": "gui.cCodePanel.run", "description": "运行C代码按钮", "defaultMessage": "运行"}, {"id": "gui.cCodePanel.description", "description": "C代码面板描述", "defaultMessage": "积木块转换的C代码"}]