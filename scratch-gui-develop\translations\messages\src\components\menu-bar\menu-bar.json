[{"id": "gui.menuBar.tutorialsLibrary", "description": "accessibility text for the tutorials button", "defaultMessage": "Tutorials"}, {"id": "gui.menuBar.debug", "description": "accessibility text for the debug button", "defaultMessage": "Debug"}, {"id": "gui.menuBar.restoreSprite", "description": "Menu bar item for restoring the last deleted sprite.", "defaultMessage": "Restore Sprite"}, {"id": "gui.menuBar.restoreSound", "description": "Menu bar item for restoring the last deleted sound.", "defaultMessage": "Restore Sound"}, {"id": "gui.menuBar.restoreCostume", "description": "Menu bar item for restoring the last deleted costume.", "defaultMessage": "<PERSON><PERSON>"}, {"id": "gui.menuBar.restore", "description": "Menu bar item for restoring the last deleted item in its disabled state.", "defaultMessage": "Rest<PERSON>"}, {"id": "gui.menuBar.saveNow", "description": "Menu bar item for saving now", "defaultMessage": "Save now"}, {"id": "gui.menuBar.saveAsCopy", "description": "Menu bar item for saving as a copy", "defaultMessage": "Save as a copy"}, {"id": "gui.menuBar.remix", "description": "Menu bar item for remixing", "defaultMessage": "Remix"}, {"id": "gui.menuBar.new", "description": "Menu bar item for creating a new project", "defaultMessage": "New"}, {"id": "gui.menuBar.file", "description": "Text for file dropdown menu", "defaultMessage": "File"}, {"id": "gui.menuBar.downloadToComputer", "description": "Menu bar item for downloading a project to your computer", "defaultMessage": "Save to your computer"}, {"id": "gui.menuBar.edit", "description": "Text for edit dropdown menu", "defaultMessage": "Edit"}, {"id": "gui.menuBar.turboModeOff", "description": "Menu bar item for turning off turbo mode", "defaultMessage": "Turn off Turbo Mode"}, {"id": "gui.menuBar.turboModeOn", "description": "Menu bar item for turning on turbo mode", "defaultMessage": "Turn on Turbo Mode"}, {"id": "gui.menuBar.modeMenu", "description": "Mode menu item in the menu bar", "defaultMessage": "Mode"}, {"id": "gui.menuBar.normalMode", "description": "April fools: resets editor to not have any pranks", "defaultMessage": "Normal mode"}, {"id": "gui.menuBar.caturdayMode", "description": "April fools: <PERSON> blocks mode", "defaultMessage": "Caturday mode"}]