import PropTypes from 'prop-types';
import React from 'react';
import bindAll from 'lodash.bindall';
import VM from 'scratch-vm';
import {connect} from 'react-redux';

import CCodePanelComponent from '../components/c-code-panel/c-code-panel.jsx';
import generateCCodeFromBlocks from '../lib/c-code-generator.js';

class CCodePanel extends React.Component {
    constructor (props) {
        super(props);
        bindAll(this, [
            'handleWorkspaceUpdate',
            'handleTargetsUpdate',
            'handleBlockDragUpdate',
            'handleRunClick',
            'createCustomCCode',
            'loadPicoC'
        ]);
        this.state = {
            codeContent: '// C代码将在这里显示',
            runStatus: null,
            runMessage: '',
            isCompiling: false,
            isRunning: false,
            picocLoaded: false
        };

        // PicoC 实例
        this.picocjs = null;

        // 动态加载 PicoC
        this.loadPicoC();
    }

    componentDidMount () {
        if (this.props.vm) {
            // 监听所有可能导致代码需要更新的事件
            this.props.vm.addListener('workspaceUpdate', this.handleWorkspaceUpdate);
            this.props.vm.addListener('targetsUpdate', this.handleTargetsUpdate);
            this.props.vm.addListener('BLOCK_DRAG_UPDATE', this.handleBlockDragUpdate);
            this.props.vm.addListener('BLOCKS_CHANGED', this.handleWorkspaceUpdate);

            // 初始化时生成一次代码
            this.updateCCode();
        }
    }

    componentDidUpdate (prevProps) {
        // 如果VM或编辑目标改变，更新代码
        if (this.props.vm !== prevProps.vm ||
            (this.props.vm && prevProps.vm &&
             this.props.vm.editingTarget !== prevProps.vm.editingTarget)) {
            this.updateCCode();
        }
    }

    componentWillUnmount () {
        if (this.props.vm) {
            this.props.vm.removeListener('workspaceUpdate', this.handleWorkspaceUpdate);
            this.props.vm.removeListener('targetsUpdate', this.handleTargetsUpdate);
            this.props.vm.removeListener('BLOCK_DRAG_UPDATE', this.handleBlockDragUpdate);
            this.props.vm.removeListener('BLOCKS_CHANGED', this.handleWorkspaceUpdate);
        }
    }

    /**
     * 动态加载 PicoC
     */
    async loadPicoC () {
        try {
            // 检查是否已经加载
            if (window.picocjs) {
                this.picocjs = window.picocjs;
                this.setState({picocLoaded: true});
                console.log('PicoC 已经加载');
                return;
            }

            console.log('开始加载 PicoC...');

            // 动态加载本地 PicoC 脚本
            const script = document.createElement('script');
            script.src = './static/picoc-test/bundle.umd.js';

            script.onload = () => {
                console.log('PicoC 脚本加载成功');
                if (window.picocjs) {
                    this.picocjs = window.picocjs;
                    this.setState({picocLoaded: true});
                    console.log('PicoC 初始化完成');
                } else {
                    console.error('PicoC 脚本加载后未找到 picocjs 对象');
                    this.setState({
                        runStatus: 'error',
                        runMessage: 'PicoC 初始化失败'
                    });
                }
            };

            script.onerror = (error) => {
                console.error('PicoC 脚本加载失败:', error);
                this.setState({
                    runStatus: 'error',
                    runMessage: 'PicoC 加载失败，请检查文件是否存在'
                });
            };

            document.head.appendChild(script);
        } catch (error) {
            console.error('PicoC 加载异常:', error);
            this.setState({
                runStatus: 'error',
                runMessage: `PicoC 加载异常: ${error.message}`
            });
        }
    }

    /**
     * 创建可执行的 C 代码，将 Scratch VM 操作嵌入到 C 代码中
     */
    createCustomCCode (originalCode) {
        if (!this.props.vm) return originalCode;

        const vm = this.props.vm;

        // 创建一个全局的 VM 控制器对象
        window.scratchVMController = {
            // 基础移动函数
            moveForward: (steps) => {
                const target = vm.editingTarget;
                if (target) {
                    target.setXY(target.x + steps, target.y);
                    vm.runtime.requestRedraw();
                }
            },
            moveBackward: (steps) => {
                const target = vm.editingTarget;
                if (target) {
                    target.setXY(target.x - steps, target.y);
                    vm.runtime.requestRedraw();
                }
            },
            turnLeft: (degrees) => {
                const target = vm.editingTarget;
                if (target) {
                    target.setDirection(target.direction - degrees);
                    vm.runtime.requestRedraw();
                }
            },
            turnRight: (degrees) => {
                const target = vm.editingTarget;
                if (target) {
                    target.setDirection(target.direction + degrees);
                    vm.runtime.requestRedraw();
                }
            },
            setPosition: (x, y) => {
                const target = vm.editingTarget;
                if (target) {
                    target.setXY(x, y);
                    vm.runtime.requestRedraw();
                }
            },

            // K1 机器人函数
            // 1. 说话函数
            say: (mode, text) => {
                console.log(`gpp_say: mode=${mode}, text=${text}`);
                const target = vm.editingTarget;
                if (target) {
                    // 清理文本中的引号
                    const cleanText = text.replace(/['"]/g, '');
                    target.say(cleanText);

                    // 如果是模式0，2秒后清除对话框
                    if (mode === 0) {
                        setTimeout(() => {
                            if (target) target.say(null);
                        }, 2000);
                    }
                }
            },

            // 2. 电机控制函数
            motorRun: (motor, speed) => {
                console.log(`Motor_Run: motor=${motor}, speed=${speed}`);
                const target = vm.editingTarget;
                if (target) {
                    if (motor === 1) {
                        // 电机1控制X轴移动
                        target.setXY(target.x + speed / 5, target.y);
                    } else if (motor === 2) {
                        // 电机2控制Y轴移动
                        target.setXY(target.x, target.y + speed / 5);
                    }
                    vm.runtime.requestRedraw();
                }
            },
            motorStop: (motor) => {
                console.log(`Motor_Stop: motor=${motor}`);
                // 停止电机，这里可以停止移动动画
            },

            // 3. 移动函数
            forward: (speed, distance) => {
                console.log(`forward: speed=${speed}, distance=${distance}`);
                const target = vm.editingTarget;
                if (target) {
                    const moveDistance = distance * (speed / 4);
                    const direction = target.direction;
                    const radians = direction * Math.PI / 180;
                    const dx = moveDistance * Math.sin(radians);
                    const dy = moveDistance * Math.cos(radians);
                    target.setXY(target.x + dx, target.y + dy);
                    vm.runtime.requestRedraw();
                }
            },
            back: (speed, distance) => {
                console.log(`back: speed=${speed}, distance=${distance}`);
                const target = vm.editingTarget;
                if (target) {
                    const moveDistance = distance * (speed / 4);
                    const direction = target.direction;
                    const radians = direction * Math.PI / 180;
                    const dx = -moveDistance * Math.sin(radians);
                    const dy = -moveDistance * Math.cos(radians);
                    target.setXY(target.x + dx, target.y + dy);
                    vm.runtime.requestRedraw();
                }
            },

            // 4. 机械手函数
            servoOpen: () => {
                console.log('servo_open: 打开机械手');
                const target = vm.editingTarget;
                if (target) {
                    // 可以在这里添加视觉效果，比如改变造型
                    target.say('机械手打开');
                    setTimeout(() => {
                        if (target) target.say(null);
                    }, 1000);
                }
            },
            servoClose: () => {
                console.log('servo_close: 关闭机械手');
                const target = vm.editingTarget;
                if (target) {
                    target.say('机械手关闭');
                    setTimeout(() => {
                        if (target) target.say(null);
                    }, 1000);
                }
            },

            // 5. 音效和LED函数
            beep: (bound, time) => {
                console.log(`beep: bound=${bound}, time=${time}`);
                const target = vm.editingTarget;
                if (target) {
                    target.say(`蜂鸣器响${time}ms`);
                    setTimeout(() => {
                        if (target) target.say(null);
                    }, 1000);
                }
            },
            led: (mode, rgb) => {
                console.log(`colorful_led: mode=${mode}, rgb=${rgb}`);
                const target = vm.editingTarget;
                if (target) {
                    target.say(`LED灯模式${mode}`);
                    setTimeout(() => {
                        if (target) target.say(null);
                    }, 1000);
                }
            },

            // 6. 延时函数
            delay: async (ms) => {
                return new Promise(resolve => {
                    setTimeout(resolve, ms);
                });
            }
        };

        // 替换原始代码中的函数调用为简单的 printf 输出
        // 这样 picoc 可以执行，同时我们通过解析输出来控制 Scratch
        let modifiedCode = originalCode;

        // 替换 K1 机器人函数调用

        // 1. 替换 gpp_say 调用
        modifiedCode = modifiedCode.replace(/gpp_say\s*\(\s*(\d+)\s*,\s*([^)]+)\s*\)/g,
            'printf("GPP_SAY:%d:%s\\n", $1, $2)');

        // 2. 替换电机控制函数
        modifiedCode = modifiedCode.replace(/Motor_Run\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)/g,
            'printf("MOTOR_RUN:%d:%d\\n", $1, $2)');

        modifiedCode = modifiedCode.replace(/Motor_Stop\s*\(\s*(\d+)\s*\)/g,
            'printf("MOTOR_STOP:%d\\n", $1)');

        // 3. 替换移动函数 - 支持更多变体
        modifiedCode = modifiedCode.replace(/forward\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)/g,
            'printf("FORWARD:%d:%d\\n", $1, $2)');

        modifiedCode = modifiedCode.replace(/back\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)/g,
            'printf("BACK:%d:%d\\n", $1, $2)');

        // 支持turnleft和turnright（无下划线版本）
        modifiedCode = modifiedCode.replace(/turnleft\s*\(\s*(\d+)\s*\)/g,
            'printf("TURN_LEFT:%d\\n", $1)');

        modifiedCode = modifiedCode.replace(/turnright\s*\(\s*(\d+)\s*\)/g,
            'printf("TURN_RIGHT:%d\\n", $1)');

        // 支持turn_left和turn_right（有下划线版本）
        modifiedCode = modifiedCode.replace(/turn_left\s*\(\s*(\d+)\s*\)/g,
            'printf("TURN_LEFT:%d\\n", $1)');

        modifiedCode = modifiedCode.replace(/turn_right\s*\(\s*(\d+)\s*\)/g,
            'printf("TURN_RIGHT:%d\\n", $1)');

        // 4. 替换机械手函数
        modifiedCode = modifiedCode.replace(/servo_open\s*\(\s*\)/g,
            'printf("SERVO_OPEN\\n")');

        modifiedCode = modifiedCode.replace(/servo_close\s*\(\s*\)/g,
            'printf("SERVO_CLOSE\\n")');

        // 5. 替换传感器函数
        modifiedCode = modifiedCode.replace(/lightsensor\s*\(\s*\)/g,
            '(printf("LIGHTSENSOR\\n"), 50)');

        modifiedCode = modifiedCode.replace(/distsensor\s*\(\s*\)/g,
            '(printf("DISTSENSOR\\n"), 30)');

        modifiedCode = modifiedCode.replace(/mic1sensor\s*\(\s*\)/g,
            '(printf("MIC1SENSOR\\n"), 40)');

        modifiedCode = modifiedCode.replace(/tracker\s*\(\s*(\d+)\s*\)/g,
            '(printf("TRACKER:%d\\n", $1), 1)');

        // 6. 替换其他函数
        modifiedCode = modifiedCode.replace(/Delay_Ms\s*\(\s*(\d+)\s*\)/g,
            'printf("DELAY:%d\\n", $1)');

        modifiedCode = modifiedCode.replace(/Get_Ps2Value\s*\(\s*\)/g,
            '(printf("GET_PS2\\n"), 128)');

        modifiedCode = modifiedCode.replace(/beep\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)/g,
            'printf("BEEP:%d:%d\\n", $1, $2)');

        modifiedCode = modifiedCode.replace(/colorful_led\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)/g,
            'printf("LED:%d:%d\\n", $1, $2)');

        // 7. 添加延时处理，让while循环等控制结构能正常工作
        // 在每个printf后添加小延时，避免输出过快，并确保换行符正确处理
        modifiedCode = modifiedCode.replace(/printf\("([^"]+)\\n"([^)]*)\);/g,
            'printf("$1\\n"$2); fflush(stdout);');

        // 8. 确保所有printf输出都有换行符
        modifiedCode = modifiedCode.replace(/printf\("([^"]+)"([^)]*)\);(?!.*\\n)/g,
            'printf("$1\\n"$2); fflush(stdout);');

        console.log('修改后的C代码:', modifiedCode);
        return modifiedCode;
    }

    handleWorkspaceUpdate () {
        console.log('工作区更新，重新生成C代码');
        this.updateCCode();
    }

    handleTargetsUpdate () {
        // 当目标（角色）更新时更新代码
        this.updateCCode();
    }

    handleBlockDragUpdate () {
        // 当块被拖动时更新代码
        this.updateCCode();
    }

    updateCCode () {
        if (!this.props.vm || !this.props.vm.editingTarget) {
            this.setState({
                codeContent: '// 没有选择角色',
                runStatus: null,
                runMessage: ''
            });
            return;
        }

        try {
            const target = this.props.vm.editingTarget;
            console.log('正在为目标生成C代码:', target.getName());
            const cCode = generateCCodeFromBlocks(target, this.props.vm);
            this.setState({
                codeContent: cCode,
                runStatus: null,
                runMessage: ''
            });
        } catch (e) {
            console.error('生成C代码时出错:', e);
            this.setState({
                codeContent: `// 生成代码时出错: ${e.message}`,
                runStatus: 'error',
                runMessage: `生成代码时出错: ${e.message}`
            });
        }
    }

    /**
     * 处理运行按钮点击事件
     */
    async handleRunClick () {
        const code = this.state.codeContent;

        // 检查代码是否为空
        if (!code || code.trim() === '' || code.includes('// 没有选择角色') || code.includes('// 没有积木块可以转换')) {
            this.setState({
                runStatus: 'error',
                runMessage: '没有可运行的代码，请先添加积木块'
            });
            return;
        }

        // 检查 PicoC 是否已加载
        if (!this.state.picocLoaded || !this.picocjs) {
            this.setState({
                runStatus: 'error',
                runMessage: 'PicoC 尚未加载完成，请稍后再试'
            });
            return;
        }

        this.setState({
            isCompiling: true,
            isRunning: false,
            runStatus: null,
            runMessage: '正在编译C代码...'
        });

        try {
            // 触发绿旗事件（启动Scratch VM）
            if (this.props.vm) {
                this.props.vm.start();
                this.props.vm.greenFlag();
            }

            // 创建可执行的 C 代码
            const executableCode = this.createCustomCCode(code);
            console.log('修改后的C代码:', executableCode);

            this.setState({
                isCompiling: false,
                isRunning: true,
                runMessage: '正在执行C代码...'
            });

            // 收集输出用于解析命令和显示
            let outputBuffer = '';
            let displayOutput = '';

            // 创建输出显示弹窗
            this.createOutputModal();

            // 使用动态加载的 picoc-js 执行代码
            console.log('开始执行C代码');

            await this.picocjs.runC(executableCode, (output) => {
                console.log('C程序输出:', output);
                outputBuffer += output;

                // 处理输出，确保换行符正确
                const processedOutput = output.replace(/\\n/g, '\n');
                displayOutput += processedOutput;

                // 更新弹窗显示
                this.updateOutputModal(displayOutput);

                // 解析输出并执行相应的 Scratch 操作
                this.parseAndExecuteCommands(output);
            });

            console.log('C代码执行完成');

            // 执行完成后更新弹窗状态
            this.finalizeOutputModal(displayOutput);

            // 触发绿旗动画
            this.triggerGreenFlag();

            this.setState({
                isRunning: false,
                runStatus: 'success',
                runMessage: 'C代码执行成功！精灵已按照程序运行。'
            });

        } catch (error) {
            console.error('C代码执行失败:', error);

            let errorMessage = error.message || '未知错误';

            // 根据错误类型提供更友好的错误信息
            if (errorMessage.includes('syntax')) {
                errorMessage = `语法错误: ${errorMessage}`;
            } else if (errorMessage.includes('undefined')) {
                errorMessage = `未定义的函数或变量: ${errorMessage}`;
            } else if (errorMessage.includes('compile')) {
                errorMessage = `编译错误: ${errorMessage}`;
            }

            this.setState({
                isCompiling: false,
                isRunning: false,
                runStatus: 'error',
                runMessage: `执行失败: ${errorMessage}`
            });
        }
    }

    /**
     * 创建C代码输出显示弹窗
     */
    createOutputModal() {
        // 移除已存在的弹窗
        const existingModal = document.getElementById('c-output-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // 创建弹窗HTML
        const modalHTML = `
            <div id="c-output-modal" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 10000;
                display: flex;
                justify-content: center;
                align-items: center;
            ">
                <div style="
                    background-color: white;
                    border-radius: 8px;
                    padding: 20px;
                    max-width: 600px;
                    max-height: 500px;
                    width: 90%;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                ">
                    <div style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 15px;
                        border-bottom: 1px solid #eee;
                        padding-bottom: 10px;
                    ">
                        <h3 style="margin: 0; color: #333;">C代码执行输出</h3>
                        <button id="close-output-modal" style="
                            background: none;
                            border: none;
                            font-size: 20px;
                            cursor: pointer;
                            color: #666;
                        ">×</button>
                    </div>
                    <div id="output-content" style="
                        background-color: #000;
                        color: #00ff00;
                        padding: 15px;
                        border-radius: 4px;
                        font-family: 'Courier New', monospace;
                        font-size: 14px;
                        white-space: pre-wrap;
                        max-height: 350px;
                        overflow-y: auto;
                        line-height: 1.6;
                        word-wrap: break-word;
                        overflow-wrap: break-word;
                    ">正在执行C代码...</div>
                    <div style="
                        margin-top: 15px;
                        text-align: right;
                    ">
                        <span id="execution-status" style="
                            color: #666;
                            font-size: 12px;
                        ">执行中...</span>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // 添加关闭事件
        const closeBtn = document.getElementById('close-output-modal');
        const modal = document.getElementById('c-output-modal');

        closeBtn.onclick = () => modal.remove();
        modal.onclick = (e) => {
            if (e.target === modal) modal.remove();
        };
    }

    /**
     * 更新输出弹窗内容
     */
    updateOutputModal(output) {
        const outputContent = document.getElementById('output-content');
        if (outputContent) {
            // 处理输出文本，确保换行符正确显示
            let processedOutput = output || '正在执行C代码...';

            // 确保换行符正确处理
            processedOutput = processedOutput.replace(/\\n/g, '\n');

            // 添加时间戳和格式化
            const lines = processedOutput.split('\n');
            const formattedLines = lines.map(line => {
                if (line.trim() && !line.includes('程序') && !line.includes('执行')) {
                    return `> ${line}`;
                }
                return line;
            });

            outputContent.textContent = formattedLines.join('\n');
            outputContent.scrollTop = outputContent.scrollHeight;
        }
    }

    /**
     * 完成输出弹窗显示
     */
    finalizeOutputModal(output) {
        const statusElement = document.getElementById('execution-status');
        if (statusElement) {
            statusElement.textContent = '执行完成';
            statusElement.style.color = '#4CAF50';
        }

        // 如果没有输出，显示提示信息
        if (!output || output.trim() === '') {
            this.updateOutputModal('程序执行完成，无控制台输出。\n精灵的移动和动作已在舞台区域显示。');
        }
    }

    /**
     * 解析 C 程序输出并执行相应的 Scratch 操作
     */
    parseAndExecuteCommands (output) {
        if (!this.props.vm) return;

        const lines = output.split('\n');
        const vm = this.props.vm;

        lines.forEach((line, index) => {
            line = line.trim();
            if (!line) return;

            console.log('解析命令:', line);

            // 添加延时，让动作有序执行
            setTimeout(() => {
                this.executeVMCommand(line, vm);
            }, index * 200); // 每个命令间隔200ms
        });
    }

    /**
     * 执行VM命令
     */
    executeVMCommand(line, vm) {
        try {
            // 获取当前活动的精灵（非舞台精灵）
            let target = vm.runtime.getEditingTarget();
            if (!target || target.isStage) {
                // 如果没有编辑目标或目标是舞台，则获取第一个非舞台精灵
                target = vm.runtime.targets.find(t => !t.isStage);
            }

            if (!target) {
                console.warn('没有找到可控制的精灵');
                return;
            }

            console.log('控制精灵:', target.sprite.name);

            // 1. 处理说话命令
            if (line.startsWith('GPP_SAY:')) {
                const parts = line.split(':');
                if (parts.length >= 3) {
                    const mode = parseInt(parts[1]);
                    const text = parts.slice(2).join(':'); // 重新组合文本，防止文本中包含冒号
                    target.say(text);
                    if (mode === 0) {
                        setTimeout(() => target.say(null), 2000);
                    }
                }
            }
            // 2. 处理移动命令
            else if (line.startsWith('FORWARD:')) {
                const parts = line.split(':');
                if (parts.length === 3) {
                    const speed = parseInt(parts[1]);
                    const distance = parseInt(parts[2]);
                    this.moveSprite(target, 'forward', speed, distance);
                }
            } else if (line.startsWith('BACK:')) {
                const parts = line.split(':');
                if (parts.length === 3) {
                    const speed = parseInt(parts[1]);
                    const distance = parseInt(parts[2]);
                    this.moveSprite(target, 'back', speed, distance);
                }
            } else if (line.startsWith('TURN_LEFT:')) {
                const parts = line.split(':');
                if (parts.length === 2) {
                    const degrees = parseInt(parts[1]);
                    this.turnSprite(target, -degrees);
                }
            } else if (line.startsWith('TURN_RIGHT:')) {
                const parts = line.split(':');
                if (parts.length === 2) {
                    const degrees = parseInt(parts[1]);
                    this.turnSprite(target, degrees);
                }
            }
            // 3. 处理机械手命令
            else if (line === 'SERVO_OPEN') {
                console.log('机械手开启');
                // 可以添加视觉效果
            } else if (line === 'SERVO_CLOSE') {
                console.log('机械手关闭');
                // 可以添加视觉效果
            }
            // 4. 处理传感器命令
            else if (line === 'LIGHTSENSOR') {
                console.log('光线传感器读取，模拟值: 50');
            } else if (line === 'DISTSENSOR') {
                console.log('距离传感器读取，模拟值: 30cm');
            } else if (line === 'MIC1SENSOR') {
                console.log('噪声传感器读取，模拟值: 40');
            } else if (line.startsWith('TRACKER:')) {
                const parts = line.split(':');
                if (parts.length === 2) {
                    const id = parseInt(parts[1]);
                    console.log(`巡线传感器${id}读取，模拟值: 1`);
                }
            }
            // 5. 处理其他命令
            else if (line.startsWith('DELAY:')) {
                const parts = line.split(':');
                if (parts.length === 2) {
                    const ms = parseInt(parts[1]);
                    console.log(`延时 ${ms}ms`);
                    // 延时在C代码执行时已经处理
                }
            } else if (line === 'GET_PS2') {
                console.log('模拟PS2输入，返回值: 128');
            } else if (line.startsWith('BEEP:')) {
                const parts = line.split(':');
                if (parts.length === 3) {
                    const bound = parseInt(parts[1]);
                    const time = parseInt(parts[2]);
                    console.log(`蜂鸣器: 频率=${bound}, 时间=${time}ms`);
                }
            } else if (line.startsWith('LED:')) {
                const parts = line.split(':');
                if (parts.length === 3) {
                    const mode = parseInt(parts[1]);
                    const rgb = parseInt(parts[2]);
                    console.log(`LED灯: 模式=${mode}, 颜色=${rgb}`);
                }
            }
        } catch (error) {
            console.error('执行VM命令失败:', line, error);
        }
    }

    /**
     * 移动精灵
     */
    moveSprite(target, direction, speed, distance) {
        if (!target) return;

        // 计算移动距离，速度作为移动的倍数
        const moveDistance = distance * (speed / 4);

        // 根据精灵的方向计算移动的x和y分量
        const currentDirection = target.direction;
        const radians = currentDirection * Math.PI / 180;

        let dx, dy;
        if (direction === 'forward') {
            dx = moveDistance * Math.sin(radians);
            dy = moveDistance * Math.cos(radians);
        } else if (direction === 'back') {
            dx = -moveDistance * Math.sin(radians);
            dy = -moveDistance * Math.cos(radians);
        }

        // 更新精灵位置
        target.setXY(target.x + dx, target.y + dy);
        console.log(`精灵${direction}: 速度=${speed}, 距离=${distance}, 新位置=(${target.x}, ${target.y})`);
    }

    /**
     * 转动精灵
     */
    turnSprite(target, degrees) {
        if (!target) return;

        // 更新精灵方向
        target.setDirection(target.direction + degrees);
        console.log(`精灵转动: ${degrees}度, 新方向=${target.direction}`);
    }

    /**
     * 触发绿旗动画（仅视觉效果，不执行积木块）
     */
    triggerGreenFlag() {
        try {
            // 只触发视觉动画，不执行积木块程序
            // 查找绿旗按钮并添加动画效果
            const greenFlagButton = document.querySelector('[class*="green-flag"]');
            if (greenFlagButton) {
                greenFlagButton.style.transform = 'scale(1.1)';
                greenFlagButton.style.transition = 'transform 0.2s';
                setTimeout(() => {
                    greenFlagButton.style.transform = 'scale(1)';
                }, 200);
                console.log('绿旗视觉动画已触发');
            } else {
                console.log('未找到绿旗按钮，跳过动画');
            }
        } catch (error) {
            console.error('触发绿旗动画失败:', error);
        }
    }

    render () {
        return (
            <CCodePanelComponent
                codeContent={this.state.codeContent}
                onRunClick={this.handleRunClick}
                runStatus={this.state.runStatus}
                runMessage={this.state.runMessage}
                isCompiling={this.state.isCompiling}
                isRunning={this.state.isRunning}
                picocLoaded={this.state.picocLoaded}
            />
        );
    }
}

CCodePanel.propTypes = {
    vm: PropTypes.instanceOf(VM)
};

const mapStateToProps = state => ({
    vm: state.scratchGui.vm
});

export default connect(
    mapStateToProps
)(CCodePanel);