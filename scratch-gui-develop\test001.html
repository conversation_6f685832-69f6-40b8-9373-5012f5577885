<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>Picoc 测试 - 使用 puts()</title>

</head>
<body>




<script src="picoc-test/bundle.umd.js"></script>

<script>
    const cprog = `
#include <stdio.h>
int main() {

  int i;
    // for循环的正确语法格式，并且初始化、条件和迭代是分开的
    for(i=20; i>=1; i--) {
        // 打印变量i的值，而不是它的地址
        printf("%d\\n",i);
    }
    printf("循环结束！\\n");
    printf("循环结束2！\\n");
    printf("循环结束3！\\n");
    return 0;
   return 0;
}
`;

    picocjs.runC(cprog, (output) => { console.log(output) });
</script>

</body>
</html>
